"""
Modernized Outfitter for the Standardized Outfit System
Replaces the old hardcoded outfitter with tech-level filtering and clean UI
"""

# Add the parent directory to the path for imports
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pygame as pg
from game_objects.standardized_outfits import (
    get_outfit_by_id, OUTFITS_REGISTRY,
    OUTFIT_CATEGORY_WEAPONS, OUTFIT_CATEGORY_DEFENSE, OUTFIT_CATEGORY_ENGINES,
    OUTFIT_CATEGORY_ELECTRONICS, OUTFIT_CATEGORY_UTILITY, OUTFIT_CATEGORY_AMMUNITION,
    OUTFIT_CATEGORY_SPECIAL, Weapon, Ammunition
)

# UI Colors
BG_COLOR = (15, 15, 25)
PANEL_COLOR = (25, 25, 40)
PANEL_BORDER = (60, 60, 80)
TITLE_COLOR = (220, 220, 255)
TEXT_COLOR = (200, 200, 220)
SELECTED_COLOR = (120, 80, 200)
BUTTON_COLOR = (50, 70, 120)
BUTTON_TEXT_COLOR = (255, 255, 255)
BUTTON_DISABLED_COLOR = (40, 40, 50)
BUTTON_DISABLED_TEXT_COLOR = (120, 120, 120)

SUCCESS_COLOR = (100, 255, 100)
WARNING_COLOR = (255, 255, 100)
ERROR_COLOR = (255, 100, 100)
NEUTRAL_COLOR = (200, 200, 200)

# Category colors
CATEGORY_COLORS = {
    OUTFIT_CATEGORY_WEAPONS: (255, 120, 120),
    OUTFIT_CATEGORY_AMMUNITION: (255, 180, 100),
    OUTFIT_CATEGORY_DEFENSE: (120, 150, 255),
    OUTFIT_CATEGORY_ENGINES: (120, 255, 120),
    OUTFIT_CATEGORY_ELECTRONICS: (255, 120, 255),
    OUTFIT_CATEGORY_UTILITY: (255, 255, 120),
    OUTFIT_CATEGORY_SPECIAL: (200, 255, 200)
}

class ModernizedOutfitter:
    """Modern outfitter interface with tech-level filtering."""

    def __init__(self, game):
        self.game = game
        self.planet = None
        self.available_outfits = []
        self.player_outfits = []
        self.current_tab = "buy"
        self.current_category = OUTFIT_CATEGORY_WEAPONS
        self.selected_outfit = None
        self.scroll_offset = 0
        self.max_visible_items = 8
        self.ui_rects = {}
        self.outfit_item_rects = []

    def open(self, planet):
        """Open outfitter on planet."""
        self.planet = planet
        if planet.tech_level == 0:
            self.game.set_status_message("This planet has no outfitter.", ERROR_COLOR)
            return False
        self._update_available_outfits()
        self._update_player_outfits()
        return True

    def _update_available_outfits(self):
        """Update available outfits based on tech level."""
        self.available_outfits = []
        for outfit in OUTFITS_REGISTRY.values():
            if (outfit.min_tech_level <= self.planet.tech_level and
                outfit.can_install_on_ship(self.game.player.ship)):
                self.available_outfits.append(outfit)
        self.available_outfits.sort(key=lambda x: (x.category, x.cost))

    def _update_player_outfits(self):
        """Update player's installed outfits."""
        self.player_outfits = []
        for outfit_id, quantity in self.game.player.installed_outfits.items():
            outfit = get_outfit_by_id(outfit_id)
            if outfit and quantity > 0:
                self.player_outfits.append((outfit, quantity))
        self.player_outfits.sort(key=lambda x: (x[0].category, x[0].name))

    def _get_outfits_in_category(self, category):
        """Get outfits in current category for current tab."""
        if self.current_tab == "buy":
            return [o for o in self.available_outfits if o.category == category]
        else:
            return [(o, q) for o, q in self.player_outfits if o.category == category]

    def run(self, screen):
        """Main outfitter loop."""
        clock = pg.time.Clock()
        running = True

        while running and self.game.running:
            clock.tick(60)

            for event in pg.event.get():
                if event.type == pg.QUIT:
                    self.game.running = False
                    running = False
                elif event.type == pg.KEYDOWN:
                    if event.key == pg.K_ESCAPE:
                        running = False
                        return "DOCKED"  # Return to docked state
                elif event.type == pg.MOUSEBUTTONDOWN:
                    if event.button == 1:
                        result = self._handle_click(pg.mouse.get_pos())
                        if result == "DOCKED":
                            running = False
                            return "DOCKED"
                elif event.type == pg.MOUSEWHEEL:
                    self._handle_scroll(event.y)

            self._draw(screen)
            pg.display.flip()

        return self.game.state

    def _handle_click(self, mouse_pos):
        """Handle mouse clicks."""
        # Tab buttons
        if 'tab_buy' in self.ui_rects and self.ui_rects['tab_buy'].collidepoint(mouse_pos):
            self.current_tab = "buy"
            self.selected_outfit = None
            self.scroll_offset = 0
        elif 'tab_sell' in self.ui_rects and self.ui_rects['tab_sell'].collidepoint(mouse_pos):
            self.current_tab = "sell"
            self.selected_outfit = None
            self.scroll_offset = 0

        # Category buttons
        for category in CATEGORY_COLORS.keys():
            if f'cat_{category}' in self.ui_rects and self.ui_rects[f'cat_{category}'].collidepoint(mouse_pos):
                self.current_category = category
                self.selected_outfit = None
                self.scroll_offset = 0

        # Outfit list items
        for i, rect in enumerate(self.outfit_item_rects):
            if rect.collidepoint(mouse_pos):
                self._select_outfit_at_index(i)
                break

        # Action button
        if 'action_button' in self.ui_rects and self.ui_rects['action_button'].collidepoint(mouse_pos):
            if self.selected_outfit:
                if self.current_tab == "buy":
                    self._buy_outfit()
                else:
                    self._sell_outfit()

        # Back button
        if 'back_button' in self.ui_rects and self.ui_rects['back_button'].collidepoint(mouse_pos):
            return "DOCKED"  # Exit to docked state

    def _draw(self, screen):
        """Draw the outfitter interface."""
        screen.fill(BG_COLOR)
        width, height = screen.get_size()

        self._draw_title(screen, width)
        self._draw_credits(screen, width)
        self._draw_tabs(screen, width)
        self._draw_categories(screen, width)
        self._draw_main_panels(screen, width, height)
        self._draw_instructions(screen, width, height)

    def _draw_title(self, screen, width):
        """Draw title."""
        title = f"Outfitter - {self.planet.name} (Tech Level {self.planet.tech_level})"
        font = pg.font.Font(None, 48)
        text_surf = font.render(title, True, TITLE_COLOR)
        text_rect = text_surf.get_rect(center=(width // 2, 40))
        screen.blit(text_surf, text_rect)

    def _draw_categories(self, screen, width):
        """Draw category buttons."""
        categories = [OUTFIT_CATEGORY_WEAPONS, OUTFIT_CATEGORY_AMMUNITION, OUTFIT_CATEGORY_DEFENSE,
                     OUTFIT_CATEGORY_ENGINES, OUTFIT_CATEGORY_ELECTRONICS, OUTFIT_CATEGORY_UTILITY]

        cat_width, cat_height, cat_y = 90, 30, 140
        total_width = len(categories) * cat_width + (len(categories) - 1) * 5
        start_x = (width - total_width) // 2
        font = pg.font.Font(None, 18)

        for i, category in enumerate(categories):
            cat_x = start_x + i * (cat_width + 5)
            cat_rect = pg.Rect(cat_x, cat_y, cat_width, cat_height)

            cat_color = CATEGORY_COLORS[category] if category == self.current_category else BUTTON_COLOR
            pg.draw.rect(screen, cat_color, cat_rect)
            pg.draw.rect(screen, PANEL_BORDER, cat_rect, 1)
            self.ui_rects[f'cat_{category}'] = cat_rect

            text_surf = font.render(category.title(), True, BUTTON_TEXT_COLOR)
            text_rect = text_surf.get_rect(center=cat_rect.center)
            screen.blit(text_surf, text_rect)

    def _draw_main_panels(self, screen, width, height):
        """Draw main outfit list and details panels."""
        panel_y = 190
        panel_height = height - panel_y - 80

        # Outfit list (left)
        list_width = width * 0.6
        list_rect = pg.Rect(20, panel_y, list_width - 20, panel_height)
        pg.draw.rect(screen, PANEL_COLOR, list_rect)
        pg.draw.rect(screen, PANEL_BORDER, list_rect, 2)
        self._draw_outfit_list(screen, list_rect)

        # Details (right)
        details_width = width * 0.4 - 40
        details_rect = pg.Rect(list_width + 20, panel_y, details_width, panel_height)
        pg.draw.rect(screen, PANEL_COLOR, details_rect)
        pg.draw.rect(screen, PANEL_BORDER, details_rect, 2)
        self._draw_outfit_details(screen, details_rect)

    def _draw_outfit_list(self, screen, list_rect):
        """Draw outfit list."""
        font = pg.font.Font(None, 24)
        small_font = pg.font.Font(None, 18)

        # Title
        title = f"Available {self.current_category.title()}" if self.current_tab == "buy" else f"Installed {self.current_category.title()}"
        title_surf = font.render(title, True, TITLE_COLOR)
        screen.blit(title_surf, (list_rect.left + 10, list_rect.top + 10))

        # Get outfits
        outfits = self._get_outfits_in_category(self.current_category)
        if not outfits:
            no_items = "No items available" if self.current_tab == "buy" else "No items installed"
            text_surf = small_font.render(no_items, True, TEXT_COLOR)
            screen.blit(text_surf, (list_rect.left + 10, list_rect.top + 50))
            return

        # Draw outfit items
        self.outfit_item_rects = []
        item_height, start_y = 45, list_rect.top + 50
        visible_outfits = outfits[self.scroll_offset:self.scroll_offset + self.max_visible_items]

        for i, outfit_data in enumerate(visible_outfits):
            item_y = start_y + i * item_height
            item_rect = pg.Rect(list_rect.left + 5, item_y, list_rect.width - 10, item_height - 2)

            # Check if selected
            is_selected = (self.selected_outfit and
                          ((self.current_tab == "buy" and outfit_data == self.selected_outfit) or
                           (self.current_tab == "sell" and outfit_data[0] == self.selected_outfit)))

            # Draw background
            bg_color = SELECTED_COLOR if is_selected else (40, 40, 55)
            pg.draw.rect(screen, bg_color, item_rect)
            pg.draw.rect(screen, PANEL_BORDER, item_rect, 1)
            self.outfit_item_rects.append(item_rect)

            # Extract outfit info
            if self.current_tab == "buy":
                outfit = outfit_data
                quantity_text = ""
            else:
                outfit, quantity = outfit_data
                quantity_text = f" x{quantity}"

            # Draw outfit name
            name_text = f"{outfit.name}{quantity_text}"
            name_surf = font.render(name_text, True, TEXT_COLOR)
            screen.blit(name_surf, (item_rect.left + 10, item_rect.top + 5))

            # Draw price
            price_text = f"{outfit.cost} cr" if self.current_tab == "buy" else f"{outfit.cost // 2} cr (sell)"
            price_surf = small_font.render(price_text, True, CATEGORY_COLORS[outfit.category])
            screen.blit(price_surf, (item_rect.left + 10, item_rect.top + 25))

            # Draw space requirement
            space_text = f"{outfit.space_required} tons"
            space_surf = small_font.render(space_text, True, NEUTRAL_COLOR)
            space_rect = space_surf.get_rect(right=item_rect.right - 10, top=item_rect.top + 25)
            screen.blit(space_surf, space_rect)

    def _draw_instructions(self, screen, width, height):
        """Draw instructions at the bottom."""
        # Instructions
        instructions = "Press ESC to return to planet | Mouse wheel to scroll | Click outfits to view details"
        font = pg.font.Font(None, 18)
        text_surf = font.render(instructions, True, TEXT_COLOR)
        text_rect = text_surf.get_rect(center=(width // 2, height - 20))
        screen.blit(text_surf, text_rect)

        # Back button
        back_width, back_height = 80, 30
        back_rect = pg.Rect(width - back_width - 20, height - back_height - 10, back_width, back_height)
        pg.draw.rect(screen, BUTTON_COLOR, back_rect)
        pg.draw.rect(screen, PANEL_BORDER, back_rect, 1)
        self.ui_rects['back_button'] = back_rect

        back_font = pg.font.Font(None, 20)
        back_text = back_font.render("Back", True, BUTTON_TEXT_COLOR)
        back_text_rect = back_text.get_rect(center=back_rect.center)
        screen.blit(back_text, back_text_rect)

    def _draw_outfit_details(self, screen, details_rect):
        """Draw outfit details."""
        font = pg.font.Font(None, 24)
        medium_font = pg.font.Font(None, 20)
        small_font = pg.font.Font(None, 18)

        # Title
        title_surf = font.render("Outfit Details", True, TITLE_COLOR)
        screen.blit(title_surf, (details_rect.left + 10, details_rect.top + 10))

        if not self.selected_outfit:
            hint_surf = small_font.render("Select an outfit to view details", True, TEXT_COLOR)
            screen.blit(hint_surf, (details_rect.left + 10, details_rect.top + 50))
            return

        outfit = self.selected_outfit
        y = details_rect.top + 50

        # Outfit name
        name_surf = font.render(outfit.name, True, TEXT_COLOR)
        screen.blit(name_surf, (details_rect.left + 10, y))
        y += 35

        # Category
        cat_surf = medium_font.render(f"Category: {outfit.category.title()}", True, CATEGORY_COLORS[outfit.category])
        screen.blit(cat_surf, (details_rect.left + 10, y))
        y += 25

        # Cost
        if self.current_tab == "buy":
            cost_text = f"Cost: {outfit.cost} credits"
            cost_color = ERROR_COLOR if self.game.player.credits < outfit.cost else SUCCESS_COLOR
        else:
            cost_text = f"Sell Value: {outfit.cost // 2} credits"
            cost_color = SUCCESS_COLOR
        cost_surf = medium_font.render(cost_text, True, cost_color)
        screen.blit(cost_surf, (details_rect.left + 10, y))
        y += 25

        # Space
        space_text = f"Space: {outfit.space_required} tons"
        space_color = ERROR_COLOR if (outfit.space_required + self.game.player.used_outfit_space >
                                     self.game.player.outfit_space and self.current_tab == "buy") else NEUTRAL_COLOR
        space_surf = medium_font.render(space_text, True, space_color)
        screen.blit(space_surf, (details_rect.left + 10, y))
        y += 30

        # Tech Level
        tech_surf = medium_font.render(f"Tech Level: {outfit.min_tech_level}", True, NEUTRAL_COLOR)
        screen.blit(tech_surf, (details_rect.left + 10, y))
        y += 25

        # Image (if available)
        if hasattr(outfit, 'outfitter_icon') and outfit.outfitter_icon:
            try:
                # Try to load and display the outfit image
                image_path = outfit.outfitter_icon
                if not os.path.isabs(image_path):
                    # Convert relative path to absolute
                    image_path = os.path.join(os.getcwd(), image_path)

                if os.path.exists(image_path):
                    outfit_image = pg.image.load(image_path)
                    # Scale image to fit in details panel
                    max_size = 80
                    image_rect = outfit_image.get_rect()
                    if image_rect.width > max_size or image_rect.height > max_size:
                        scale_factor = min(max_size / image_rect.width, max_size / image_rect.height)
                        new_size = (int(image_rect.width * scale_factor), int(image_rect.height * scale_factor))
                        outfit_image = pg.transform.scale(outfit_image, new_size)

                    # Center the image
                    image_x = details_rect.left + (details_rect.width - outfit_image.get_width()) // 2
                    screen.blit(outfit_image, (image_x, y))
                    y += outfit_image.get_height() + 10
            except (pg.error, FileNotFoundError, OSError):
                # If image loading fails, show placeholder text
                no_image_surf = small_font.render("[No Image]", True, (100, 100, 100))
                screen.blit(no_image_surf, (details_rect.left + 10, y))
                y += 20

        # Description
        if outfit.description:
            desc_surf = small_font.render(outfit.description[:60] + "...", True, TEXT_COLOR)
            screen.blit(desc_surf, (details_rect.left + 10, y))
            y += 40

        # Basic stats for weapons
        if isinstance(outfit, Weapon):
            stats = [f"Damage: {outfit.damage}", f"Fire Rate: {outfit.fire_rate:.1f}/sec",
                    f"Range: {outfit.range}", f"Energy: {outfit.energy_usage}"]
            for stat in stats[:4]:  # Limit to 4 stats
                stat_surf = small_font.render(stat, True, NEUTRAL_COLOR)
                screen.blit(stat_surf, (details_rect.left + 10, y))
                y += 18

        # Buy/Sell button
        button_width, button_height = 120, 40
        button_x = details_rect.centerx - button_width // 2
        button_y = details_rect.bottom - 60
        button_rect = pg.Rect(button_x, button_y, button_width, button_height)

        if self.current_tab == "buy":
            can_afford = self.game.player.credits >= outfit.cost
            has_space = (self.game.player.used_outfit_space + outfit.space_required <= self.game.player.outfit_space)
            can_buy = can_afford and has_space
            button_color = BUTTON_COLOR if can_buy else BUTTON_DISABLED_COLOR
            text_color = BUTTON_TEXT_COLOR if can_buy else BUTTON_DISABLED_TEXT_COLOR
            button_text = "Buy"
        else:
            button_color = BUTTON_COLOR
            text_color = BUTTON_TEXT_COLOR
            button_text = "Sell"

        pg.draw.rect(screen, button_color, button_rect)
        pg.draw.rect(screen, PANEL_BORDER, button_rect, 2)
        self.ui_rects['action_button'] = button_rect

        text_surf = pg.font.Font(None, 28).render(button_text, True, text_color)
        text_rect = text_surf.get_rect(center=button_rect.center)
        screen.blit(text_surf, text_rect)

        # Show outfit space usage
        space_used = self.game.player.used_outfit_space
        space_total = self.game.player.outfit_space
        space_text = f"Outfit Space: {space_used}/{space_total} tons"
        space_surf = pg.font.Font(None, 18).render(space_text, True, TEXT_COLOR)
        space_rect = space_surf.get_rect(center=(details_rect.centerx, details_rect.bottom - 15))
        screen.blit(space_surf, space_rect)

    def _draw_credits(self, screen, width):
        """Draw player credits."""
        credits_text = f"Credits: {self.game.player.credits:,}"
        font = pg.font.Font(None, 32)
        text_surf = font.render(credits_text, True, SUCCESS_COLOR)
        text_rect = text_surf.get_rect(topright=(width - 20, 20))
        screen.blit(text_surf, text_rect)

    def _draw_tabs(self, screen, width):
        """Draw buy/sell tabs."""
        tab_width, tab_height, tab_y = 120, 40, 80

        # Buy tab
        buy_rect = pg.Rect(width // 2 - tab_width - 10, tab_y, tab_width, tab_height)
        buy_color = SELECTED_COLOR if self.current_tab == "buy" else BUTTON_COLOR
        pg.draw.rect(screen, buy_color, buy_rect)
        pg.draw.rect(screen, PANEL_BORDER, buy_rect, 2)
        self.ui_rects['tab_buy'] = buy_rect

        font = pg.font.Font(None, 28)
        text_surf = font.render("Buy", True, BUTTON_TEXT_COLOR)
        text_rect = text_surf.get_rect(center=buy_rect.center)
        screen.blit(text_surf, text_rect)

        # Sell tab
        sell_rect = pg.Rect(width // 2 + 10, tab_y, tab_width, tab_height)
        sell_color = SELECTED_COLOR if self.current_tab == "sell" else BUTTON_COLOR
        pg.draw.rect(screen, sell_color, sell_rect)
        pg.draw.rect(screen, PANEL_BORDER, sell_rect, 2)
        self.ui_rects['tab_sell'] = sell_rect

        text_surf = font.render("Sell", True, BUTTON_TEXT_COLOR)
        text_rect = text_surf.get_rect(center=sell_rect.center)
        screen.blit(text_surf, text_rect)

    def _handle_scroll(self, scroll_y):
        """Handle mouse wheel scrolling."""
        outfits = self._get_outfits_in_category(self.current_category)
        max_scroll = max(0, len(outfits) - self.max_visible_items)

        if scroll_y > 0:  # Scroll up
            self.scroll_offset = max(0, self.scroll_offset - 1)
        elif scroll_y < 0:  # Scroll down
            self.scroll_offset = min(max_scroll, self.scroll_offset + 1)

    def _select_outfit_at_index(self, index):
        """Select outfit at given index."""
        outfits = self._get_outfits_in_category(self.current_category)
        actual_index = index + self.scroll_offset

        if 0 <= actual_index < len(outfits):
            if self.current_tab == "buy":
                self.selected_outfit = outfits[actual_index]
            else:
                self.selected_outfit = outfits[actual_index][0]

    def _buy_outfit(self):
        """Buy the selected outfit."""
        outfit = self.selected_outfit
        player = self.game.player

        # Check affordability
        if player.credits < outfit.cost:
            self.game.set_status_message("Insufficient credits!", ERROR_COLOR)
            return

        # Check space
        if player.used_outfit_space + outfit.space_required > player.outfit_space:
            self.game.set_status_message("Insufficient outfit space!", ERROR_COLOR)
            return

        # Handle ammunition
        if isinstance(outfit, Ammunition):
            if player.load_ammo(outfit.id):
                player.credits -= outfit.cost
                self.game.set_status_message(f"Purchased and loaded {outfit.name}!", SUCCESS_COLOR)
            else:
                self.game.set_status_message("No compatible launchers found!", WARNING_COLOR)
        else:
            # Install outfit
            if player.install_outfit(outfit.id):
                player.credits -= outfit.cost
                self.game.set_status_message(f"Purchased {outfit.name}!", SUCCESS_COLOR)
                self._update_player_outfits()

    def _sell_outfit(self):
        """Sell the selected outfit."""
        outfit = self.selected_outfit
        player = self.game.player

        # Check ownership
        if outfit.id not in player.installed_outfits or player.installed_outfits[outfit.id] <= 0:
            self.game.set_status_message(f"You don't own {outfit.name}!", ERROR_COLOR)
            return

        # Remove outfit
        if player.remove_outfit(outfit.id):
            sell_price = outfit.cost // 2
            player.credits += sell_price
            self.game.set_status_message(f"Sold {outfit.name} for {sell_price} credits!", SUCCESS_COLOR)
            self._update_player_outfits()

            # Clear selection if no more left
            if outfit.id not in player.installed_outfits:
                self.selected_outfit = None
