"""
Ship menu system for Escape Velocity Py.
This module handles the player's ship management interface.
"""

import pygame as pg

# Constants
BACKGROUND_COLOR = (20, 20, 40)  # Dark blue
PANEL_COLOR = (40, 40, 60)  # Slightly lighter blue
HIGHLIGHT_COLOR = (60, 60, 100)  # Highlighted panel
TEXT_COLOR = (200, 200, 200)  # Light gray
TITLE_COLOR = (255, 255, 255)  # White
STAT_GOOD_COLOR = (100, 255, 100)  # Green
STAT_BAD_COLOR = (255, 100, 100)  # Red
STAT_NEUTRAL_COLOR = (255, 255, 100)  # Yellow
BUTTON_COLOR = (80, 80, 150)  # Blue
BUTTON_HOVER_COLOR = (100, 100, 180)  # Lighter blue
BUTTON_TEXT_COLOR = (255, 255, 255)  # White

# Tab constants
TAB_OVERVIEW = "Overview"
TAB_CARGO = "Cargo"
TAB_OUTFITS = "Outfits"
TAB_CREW = "Crew"
TAB_SYSTEMS = "Systems"

class ShipMenu:
    """Class to handle the player's ship management interface."""

    def __init__(self, game):
        """
        Initialize the ship menu.

        Args:
            game: The main game object
        """
        self.game = game
        self.current_tab = TAB_OVERVIEW
        self.tabs = [TAB_OVERVIEW, TAB_CARGO, TAB_OUTFITS, TAB_CREW, TAB_SYSTEMS]

        # UI elements
        self.tab_rects = {}
        self.back_button_rect = None

        # Scrolling for cargo and outfits
        self.scroll_offset = 0
        self.max_visible_items = 10
        self.scroll_up_rect = None
        self.scroll_down_rect = None

        # Ship rotation for 3D effect
        self.ship_rotation = 0
        self.ship_rotation_speed = 0.5

    def show_ship_menu(self, screen):
        """
        Display the ship menu.

        Args:
            screen: The pygame screen to draw on

        Returns:
            str: The next state to transition to
        """
        # Set up UI elements
        self._setup_ui(screen)

        # Main menu loop
        running = True
        while running and self.game.running:
            screen.fill(BACKGROUND_COLOR)

            # Update ship rotation using math module
            self.ship_rotation = (self.ship_rotation + self.ship_rotation_speed) % 360

            # Draw tabs
            self._draw_tabs(screen)

            # Draw content based on current tab
            if self.current_tab == TAB_OVERVIEW:
                self._draw_overview_tab(screen)
            elif self.current_tab == TAB_CARGO:
                self._draw_cargo_tab(screen)
            elif self.current_tab == TAB_OUTFITS:
                self._draw_outfits_tab(screen)
            elif self.current_tab == TAB_CREW:
                self._draw_crew_tab(screen)
            elif self.current_tab == TAB_SYSTEMS:
                self._draw_systems_tab(screen)

            # Draw back button
            mouse_pos = pg.mouse.get_pos()
            button_color = BUTTON_HOVER_COLOR if self.back_button_rect.collidepoint(mouse_pos) else BUTTON_COLOR
            pg.draw.rect(screen, button_color, self.back_button_rect)
            self.game.draw_text("Back", 24, BUTTON_TEXT_COLOR,
                               self.back_button_rect.centerx, self.back_button_rect.centery, align="center")

            # Handle events
            for event in pg.event.get():
                if event.type == pg.QUIT:
                    self.game.running = False
                    running = False

                elif event.type == pg.KEYDOWN:
                    if event.key == pg.K_ESCAPE or event.key == pg.K_p:
                        running = False

                elif event.type == pg.MOUSEBUTTONDOWN:
                    mouse_pos = pg.mouse.get_pos()

                    # Check if a tab was clicked
                    for tab, rect in self.tab_rects.items():
                        if rect.collidepoint(mouse_pos):
                            self.current_tab = tab
                            self.scroll_offset = 0  # Reset scroll when changing tabs
                            break

                    # Check if back button was clicked
                    if self.back_button_rect.collidepoint(mouse_pos):
                        running = False

                    # Check scroll buttons if visible
                    if self.scroll_up_rect and self.scroll_up_rect.collidepoint(mouse_pos):
                        self.scroll_offset = max(0, self.scroll_offset - 1)
                    elif self.scroll_down_rect and self.scroll_down_rect.collidepoint(mouse_pos):
                        # Max offset depends on the current tab
                        if self.current_tab == TAB_CARGO:
                            max_items = len(self.game.player.cargo)
                        elif self.current_tab == TAB_OUTFITS:
                            max_items = len(self.game.player.outfits)
                        else:
                            max_items = 0

                        max_offset = max(0, max_items - self.max_visible_items)
                        self.scroll_offset = min(max_offset, self.scroll_offset + 1)

            pg.display.flip()
            self.game.clock.tick(60)  # Use a default FPS of 60

        return "PLAYING"

    def _setup_ui(self, screen):
        """Set up UI elements for the ship menu."""
        width, height = screen.get_width(), screen.get_height()

        # Tab buttons
        tab_width = width / len(self.tabs)
        for i, tab in enumerate(self.tabs):
            self.tab_rects[tab] = pg.Rect(i * tab_width, 0, tab_width, 50)

        # Back button
        self.back_button_rect = pg.Rect(width - 150, height - 80, 100, 50)

        # Scroll buttons (only shown in certain tabs)
        button_size = 30
        self.scroll_up_rect = pg.Rect(width - 50, 100, button_size, button_size)
        self.scroll_down_rect = pg.Rect(width - 50, height - 150, button_size, button_size)

    def _draw_tabs(self, screen):
        """Draw the tab buttons at the top of the screen."""
        for tab, rect in self.tab_rects.items():
            color = HIGHLIGHT_COLOR if tab == self.current_tab else PANEL_COLOR
            pg.draw.rect(screen, color, rect)
            pg.draw.rect(screen, TEXT_COLOR, rect, 2)  # Border

            self.game.draw_text(tab, 24, TEXT_COLOR,
                               rect.centerx, rect.centery, align="center")

    def _draw_overview_tab(self, screen):
        """Draw the overview tab content."""
        width, height = screen.get_width(), screen.get_height()

        # Draw ship image/model
        ship_panel = pg.Rect(50, 100, width * 0.4, height - 200)
        pg.draw.rect(screen, PANEL_COLOR, ship_panel)
        pg.draw.rect(screen, TEXT_COLOR, ship_panel, 2)

        # Draw rotated ship image
        if self.game.player.ship.image:
            # Create a copy of the original image for rotation
            ship_image = pg.transform.scale(self.game.player.ship.image,
                                           (int(ship_panel.width * 0.7), int(ship_panel.height * 0.7)))

            # Apply rotation for 3D effect
            rotated_image = pg.transform.rotate(ship_image, self.ship_rotation)

            # Get the rect of the rotated image and center it
            rotated_rect = rotated_image.get_rect(center=ship_panel.center)

            # Draw the rotated image
            screen.blit(rotated_image, rotated_rect)
        else:
            # Draw placeholder if no image
            self.game.draw_text("Ship Model", 36, TEXT_COLOR,
                               ship_panel.centerx, ship_panel.centery, align="center")

        # Draw ship name and class
        self.game.draw_text(f"{self.game.player.ship_name}", 36, TITLE_COLOR,
                           width * 0.75, 100, align="center")
        self.game.draw_text(f"Class: {self.game.player.ship.ship_class.capitalize()}", 24, TEXT_COLOR,
                           width * 0.75, 140, align="center")
        self.game.draw_text(f"Size: {self.game.player.ship.size.capitalize()}", 24, TEXT_COLOR,
                           width * 0.75, 170, align="center")

        # Draw ship stats
        stats_panel = pg.Rect(width * 0.5, 200, width * 0.45, height - 300)
        pg.draw.rect(screen, PANEL_COLOR, stats_panel)
        pg.draw.rect(screen, TEXT_COLOR, stats_panel, 2)

        # Stats title
        self.game.draw_text("Ship Specifications", 28, TITLE_COLOR,
                           stats_panel.centerx, stats_panel.top + 30, align="center")

        # Draw stats in two columns
        left_col_x = stats_panel.left + 30
        right_col_x = stats_panel.centerx + 30
        start_y = stats_panel.top + 80
        line_height = 30

        # Left column stats
        self.game.draw_text(f"Hull: {self.game.player.armor}/{self.game.player.max_armor}", 20, STAT_GOOD_COLOR,
                           left_col_x, start_y, align="topleft")
        self.game.draw_text(f"Shields: {int(self.game.player.shields)}/{self.game.player.max_shields}", 20, STAT_GOOD_COLOR,
                           left_col_x, start_y + line_height, align="topleft")
        self.game.draw_text(f"Cargo Space: {sum(self.game.player.cargo.values())}/{self.game.player.cargo_space} tons", 20, STAT_NEUTRAL_COLOR,
                           left_col_x, start_y + 2*line_height, align="topleft")
        self.game.draw_text(f"Outfit Space: {self.game.player.used_outfit_space}/{self.game.player.outfit_space} tons", 20, STAT_NEUTRAL_COLOR,
                           left_col_x, start_y + 3*line_height, align="topleft")
        self.game.draw_text(f"Credits: {self.game.player.credits}", 20, STAT_NEUTRAL_COLOR,
                           left_col_x, start_y + 4*line_height, align="topleft")

        # Right column stats
        self.game.draw_text(f"Speed: {self.game.player.max_speed:.1f}", 20, STAT_GOOD_COLOR,
                           right_col_x, start_y, align="topleft")
        self.game.draw_text(f"Acceleration: {self.game.player.acceleration:.1f}", 20, STAT_GOOD_COLOR,
                           right_col_x, start_y + line_height, align="topleft")
        self.game.draw_text(f"Turn Rate: {self.game.player.turn_rate:.1f}", 20, STAT_GOOD_COLOR,
                           right_col_x, start_y + 2*line_height, align="topleft")

        # Draw ship description
        desc_panel = pg.Rect(50, height - 90, width - 200, 70)
        pg.draw.rect(screen, PANEL_COLOR, desc_panel)
        pg.draw.rect(screen, TEXT_COLOR, desc_panel, 2)

        self.game.draw_text(self.game.player.ship.description, 16, TEXT_COLOR,
                           desc_panel.left + 10, desc_panel.top + 10, align="topleft")

    def _draw_cargo_tab(self, screen):
        """Draw the cargo tab content."""
        width, height = screen.get_width(), screen.get_height()

        # Draw cargo panel
        cargo_panel = pg.Rect(50, 100, width - 100, height - 200)
        pg.draw.rect(screen, PANEL_COLOR, cargo_panel)
        pg.draw.rect(screen, TEXT_COLOR, cargo_panel, 2)

        # Draw cargo title and summary
        self.game.draw_text("Cargo Manifest", 32, TITLE_COLOR,
                           cargo_panel.centerx, cargo_panel.top + 30, align="center")

        cargo_used = sum(self.game.player.cargo.values())
        self.game.draw_text(f"Cargo Space: {cargo_used}/{self.game.player.cargo_space} tons", 24, STAT_NEUTRAL_COLOR,
                           cargo_panel.centerx, cargo_panel.top + 70, align="center")

        # Draw scroll buttons if needed
        if len(self.game.player.cargo) > self.max_visible_items:
            pg.draw.rect(screen, BUTTON_COLOR, self.scroll_up_rect)
            pg.draw.rect(screen, BUTTON_COLOR, self.scroll_down_rect)
            self.game.draw_text("▲", 20, BUTTON_TEXT_COLOR,
                               self.scroll_up_rect.centerx, self.scroll_up_rect.centery, align="center")
            self.game.draw_text("▼", 20, BUTTON_TEXT_COLOR,
                               self.scroll_down_rect.centerx, self.scroll_down_rect.centery, align="center")

        # Draw cargo list
        if not self.game.player.cargo:
            self.game.draw_text("No cargo", 28, TEXT_COLOR,
                               cargo_panel.centerx, cargo_panel.centery, align="center")
            return

        # Draw column headers
        header_y = cargo_panel.top + 120
        self.game.draw_text("Commodity", 24, TITLE_COLOR,
                           cargo_panel.left + 50, header_y, align="topleft")
        self.game.draw_text("Quantity", 24, TITLE_COLOR,
                           cargo_panel.left + 350, header_y, align="topleft")
        self.game.draw_text("Value (est.)", 24, TITLE_COLOR,
                           cargo_panel.left + 500, header_y, align="topleft")

        # Draw horizontal line
        pg.draw.line(screen, TEXT_COLOR,
                    (cargo_panel.left + 20, header_y + 30),
                    (cargo_panel.right - 20, header_y + 30), 2)

        # Get visible cargo items
        cargo_items = list(self.game.player.cargo.items())
        visible_items = cargo_items[self.scroll_offset:self.scroll_offset + self.max_visible_items]

        # Draw cargo items
        for i, (commodity_id, quantity) in enumerate(visible_items):
            y = header_y + 50 + i * 40

            # Get commodity info
            from game_objects.commodities import COMMODITIES
            commodity = COMMODITIES.get(commodity_id)
            if not commodity:
                continue

            # Draw commodity name
            name_color = TEXT_COLOR
            if commodity.illegal:
                name_color = STAT_BAD_COLOR  # Red for illegal goods
            self.game.draw_text(commodity.name, 20, name_color,
                               cargo_panel.left + 50, y, align="topleft")

            # Draw quantity
            self.game.draw_text(f"{quantity} tons", 20, TEXT_COLOR,
                               cargo_panel.left + 350, y, align="topleft")

            # Draw estimated value (base price * quantity)
            est_value = commodity.base_price * quantity
            self.game.draw_text(f"{est_value} cr", 20, STAT_GOOD_COLOR,
                               cargo_panel.left + 500, y, align="topleft")

    def _draw_outfits_tab(self, screen):
        """Draw the outfits tab content."""
        width, height = screen.get_width(), screen.get_height()

        # Draw outfits panel
        outfits_panel = pg.Rect(50, 100, width - 100, height - 200)
        pg.draw.rect(screen, PANEL_COLOR, outfits_panel)
        pg.draw.rect(screen, TEXT_COLOR, outfits_panel, 2)

        # Draw outfits title and summary
        self.game.draw_text("Installed Outfits", 32, TITLE_COLOR,
                           outfits_panel.centerx, outfits_panel.top + 30, align="center")

        self.game.draw_text(f"Outfit Space: {self.game.player.used_outfit_space}/{self.game.player.outfit_space} tons", 24, STAT_NEUTRAL_COLOR,
                           outfits_panel.centerx, outfits_panel.top + 70, align="center")

        # Draw scroll buttons if needed
        if len(self.game.player.outfits) > self.max_visible_items:
            pg.draw.rect(screen, BUTTON_COLOR, self.scroll_up_rect)
            pg.draw.rect(screen, BUTTON_COLOR, self.scroll_down_rect)
            self.game.draw_text("▲", 20, BUTTON_TEXT_COLOR,
                               self.scroll_up_rect.centerx, self.scroll_up_rect.centery, align="center")
            self.game.draw_text("▼", 20, BUTTON_TEXT_COLOR,
                               self.scroll_down_rect.centerx, self.scroll_down_rect.centery, align="center")

        # Draw outfits list
        if not self.game.player.outfits:
            self.game.draw_text("No outfits installed", 28, TEXT_COLOR,
                               outfits_panel.centerx, outfits_panel.centery, align="center")
            return

        # Draw column headers
        header_y = outfits_panel.top + 120
        self.game.draw_text("Outfit", 24, TITLE_COLOR,
                           outfits_panel.left + 50, header_y, align="topleft")
        self.game.draw_text("Quantity", 24, TITLE_COLOR,
                           outfits_panel.left + 350, header_y, align="topleft")
        self.game.draw_text("Space Used", 24, TITLE_COLOR,
                           outfits_panel.left + 500, header_y, align="topleft")

        # Draw horizontal line
        pg.draw.line(screen, TEXT_COLOR,
                    (outfits_panel.left + 20, header_y + 30),
                    (outfits_panel.right - 20, header_y + 30), 2)

        # Get visible outfit items
        outfit_items = list(self.game.player.outfits.items())
        visible_items = outfit_items[self.scroll_offset:self.scroll_offset + self.max_visible_items]

        # Draw outfit items
        for i, (outfit_id, quantity) in enumerate(visible_items):
            y = header_y + 50 + i * 40

            # Get outfit info
            from game_objects.outfits import get_outfit_by_id
            outfit = get_outfit_by_id(outfit_id)
            if not outfit:
                continue

            # Draw outfit name
            self.game.draw_text(outfit.name, 20, TEXT_COLOR,
                               outfits_panel.left + 50, y, align="topleft")

            # Draw quantity
            self.game.draw_text(f"{quantity}", 20, TEXT_COLOR,
                               outfits_panel.left + 350, y, align="topleft")

            # Draw space used
            space_used = outfit.space_required * quantity
            self.game.draw_text(f"{space_used} tons", 20, TEXT_COLOR,
                               outfits_panel.left + 500, y, align="topleft")

    def _draw_crew_tab(self, screen):
        """Draw the crew tab content (placeholder for future implementation)."""
        width, height = screen.get_width(), screen.get_height()

        # Draw crew panel
        crew_panel = pg.Rect(50, 100, width - 100, height - 200)
        pg.draw.rect(screen, PANEL_COLOR, crew_panel)
        pg.draw.rect(screen, TEXT_COLOR, crew_panel, 2)

        # Draw placeholder text
        self.game.draw_text("Crew Management", 32, TITLE_COLOR,
                           crew_panel.centerx, crew_panel.top + 30, align="center")

        self.game.draw_text("This feature is not yet implemented", 28, TEXT_COLOR,
                           crew_panel.centerx, crew_panel.centery, align="center")

        self.game.draw_text("Future feature: Assign crew to different ship systems", 20, TEXT_COLOR,
                           crew_panel.centerx, crew_panel.centery + 50, align="center")
        self.game.draw_text("for bonuses to performance, combat, and special abilities", 20, TEXT_COLOR,
                           crew_panel.centerx, crew_panel.centery + 80, align="center")

    def _draw_systems_tab(self, screen):
        """Draw the systems tab content (placeholder for future implementation)."""
        width, height = screen.get_width(), screen.get_height()

        # Draw systems panel
        systems_panel = pg.Rect(50, 100, width - 100, height - 200)
        pg.draw.rect(screen, PANEL_COLOR, systems_panel)
        pg.draw.rect(screen, TEXT_COLOR, systems_panel, 2)

        # Draw placeholder text
        self.game.draw_text("Ship Systems", 32, TITLE_COLOR,
                           systems_panel.centerx, systems_panel.top + 30, align="center")

        self.game.draw_text("This feature is not yet implemented", 28, TEXT_COLOR,
                           systems_panel.centerx, systems_panel.centery, align="center")

        self.game.draw_text("Future feature: Adjust power allocation to different ship systems", 20, TEXT_COLOR,
                           systems_panel.centerx, systems_panel.centery + 50, align="center")
        self.game.draw_text("such as engines, weapons, shields, and special equipment", 20, TEXT_COLOR,
                           systems_panel.centerx, systems_panel.centery + 80, align="center")
