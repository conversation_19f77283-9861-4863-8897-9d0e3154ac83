"""
AI Core Module - Base AI ship class and constants for Escape Velocity Py
This module contains the core AI ship class and shared constants.
"""
import pygame as pg
import random
import math
from game_objects.ships import get_ship_by_id
from game_objects.standardized_outfits import *
from game_objects.example_outfits import *
from game_objects.projectiles import LaserProjectile, MissileProjectile
from game_objects.sprite_manager import load_sprite

# --- AI Ship Constants ---
AI_DRAG_FACTOR = 0.015
AI_SENSOR_RANGE = 400
AI_PATROL_RADIUS = 300
AI_WEAPON_RANGE = 300

# --- AI States ---
AI_STATE_IDLE = "IDLE"
AI_STATE_PATROLLING = "PATROLLING"
AI_STATE_ATTACKING = "ATTACKING"
AI_STATE_FLEEING = "FLEEING"
AI_STATE_TRADING = "TRADING"
AI_STATE_DISABLED = "DISABLED"

# --- Combat Sub-States ---
COMBAT_STATE_ENGAGING = "ENGAGING"
COMBAT_STATE_PURSUING = "PURSUING"
COMBAT_STATE_FLANKING = "FLANKING"
COMBAT_STATE_RETREATING = "RETREATING"
COMBAT_STATE_MISSILE_ATTACK = "MISSILE_ATTACK"

# --- AI Personality Types ---
AI_PERSONALITY_AGGRESSIVE = "aggressive"
AI_PERSONALITY_DEFENSIVE = "defensive"
AI_PERSONALITY_BALANCED = "balanced"
AI_PERSONALITY_COWARD = "coward"

# --- Weapon Engagement Ranges ---
FIXED_WEAPON_OPTIMAL_RANGE = 150
TURRET_WEAPON_OPTIMAL_RANGE = 200
MISSILE_WEAPON_OPTIMAL_RANGE = 300

class AIShipCore(pg.sprite.Sprite):
    """
    Core AI Ship class that handles basic ship functionality.
    Combat, navigation, and state logic are handled by separate modules.
    """

    def __init__(self, game, pos_x, pos_y, faction_id, ship_id="scout"):
        super().__init__()
        self.game = game
        self.faction_id = faction_id

        # Get the ship from the ships module
        self.ship = get_ship_by_id(ship_id)
        if not self.ship:
            self.ship = get_ship_by_id("scout")

        self.ship_id = ship_id

        # Try to load the ship's spritesheet
        print(f"AI Ship: Attempting to load spritesheet for ship: {ship_id} (size: {self.ship.size})")
        self.spritesheet = load_sprite("ship", ship_id, self.ship.size)

        # Load ship image
        if self.spritesheet and self.spritesheet.image:
            self.image_orig = self.spritesheet.get_frame_by_angle(0)
            if not self.image_orig:
                self.image_orig = pg.Surface((32, 32))
                self.image_orig.fill((100, 100, 200))
        else:
            self.image_orig = pg.Surface((32, 32))
            self.image_orig.fill((100, 100, 200))

        self.image_orig.set_colorkey((0, 0, 0))
        self.image = self.image_orig.copy()
        self.rect = self.image.get_rect()

        # Position and movement
        self.pos = pg.math.Vector2(pos_x, pos_y)
        self.rect.center = self.pos
        self.vel = pg.math.Vector2(random.uniform(-1,1), random.uniform(-1,1)).normalize() * random.uniform(0, self.ship.max_speed/2)
        self.angle = random.uniform(0, 360)
        self.target_angle = self.angle
        self.rotate_image()

        # Ship identification
        self.ship_type = self.ship.ship_class

        # AI State Management
        self.ai_state = AI_STATE_PATROLLING
        self.combat_state = COMBAT_STATE_ENGAGING
        self.target_entity = None
        self.patrol_target_pos = None
        self.state_timer = 0

        # AI Personality (affects behavior)
        self.personality = self._determine_personality()

        # Ship stats from base ship
        self._initialize_ship_stats()

        # Weapons and equipment
        self.weapons = []
        self.active_weapon_index = 0
        self.projectiles = pg.sprite.Group()

        # Equip the ship with appropriate weapons
        self._equip_ai_weapons()

        # Combat-related attributes
        self.weapon_range = self._calculate_weapon_range()
        self.has_fixed_weapons = self._has_weapon_type(MOUNT_TYPE_FIXED)
        self.has_turret_weapons = self._has_weapon_type(MOUNT_TYPE_TURRET)
        self.has_missile_weapons = self._has_missile_weapons()

        # Shield recharge
        self.shield_recharge_timer = 0
        self.shield_recharge_delay = 180  # 3 seconds at 60 FPS

        print(f"AI Ship created: {self.ship_type} ({self.faction_id}) with {len(self.weapons)} weapons")

    def _determine_personality(self):
        """Determine AI personality based on faction and ship type."""
        # This could be expanded to be faction-specific
        personalities = [AI_PERSONALITY_AGGRESSIVE, AI_PERSONALITY_DEFENSIVE, AI_PERSONALITY_BALANCED]
        return random.choice(personalities)

    def _initialize_ship_stats(self):
        """Initialize ship statistics from base ship."""
        # Ship stats from base ship
        self.base_max_shields = self.ship.max_shields
        self.base_shields = self.ship.shields
        self.base_max_armor = self.ship.max_armor
        self.base_armor = self.ship.armor
        self.base_acceleration = self.ship.acceleration
        self.base_turn_rate = self.ship.turn_rate
        self.base_max_speed = self.ship.max_speed

        # Current stats (will be modified by outfits)
        self.max_shields = self.base_max_shields
        self.shields = self.base_shields
        self.max_armor = self.base_max_armor
        self.armor = self.base_armor
        self.health = self.base_armor  # AI ships use 'health' for armor
        self.max_health = self.base_max_armor
        self.acceleration = self.base_acceleration
        self.turn_rate = self.base_turn_rate
        self.max_speed = self.base_max_speed

    def _calculate_weapon_range(self):
        """Calculate the maximum range of all weapons."""
        if not self.weapons:
            return AI_WEAPON_RANGE
        return max(weapon.range for weapon in self.weapons)

    def _has_weapon_type(self, mount_type):
        """Check if ship has weapons of a specific mount type."""
        return any(weapon.mount_type == mount_type for weapon in self.weapons)

    def _has_missile_weapons(self):
        """Check if ship has missile/ammunition-based weapons."""
        return any(weapon.uses_ammo for weapon in self.weapons)

    def _equip_ai_weapons(self):
        """Equip AI ship with weapons appropriate to its size and faction."""
        # Basic weapons for all AI ships
        if self.ship.size == "small":
            self.install_outfit("pulse_laser")
            if random.random() < 0.3:  # 30% chance for missiles
                self.install_outfit("missile_rack")
                self.load_ammo("light_missile")
        elif self.ship.size == "medium":
            self.install_outfit("laser_cannon")
            if random.random() < 0.5:  # 50% chance for missiles
                self.install_outfit("missile_rack")
                self.load_ammo("guided_missile")
            if random.random() < 0.3:  # 30% chance for turret
                self.install_outfit("laser_turret")
        elif self.ship.size == "large":
            self.install_outfit("heavy_laser")
            self.install_outfit("laser_turret")
            if random.random() < 0.7:  # 70% chance for heavy missiles
                self.install_outfit("heavy_missile_launcher")
                self.load_ammo("heavy_missile")
        elif self.ship.size == "capital":
            self.install_outfit("capital_laser")
            self.install_outfit("heavy_turret")
            self.install_outfit("torpedo_launcher")
            self.load_ammo("torpedo")

    def install_outfit(self, outfit_id):
        """Install an outfit on this AI ship."""
        try:
            from game_objects.standardized_outfits import OUTFITS_REGISTRY, OUTFIT_CATEGORY_WEAPONS
            if outfit_id in OUTFITS_REGISTRY:
                outfit = OUTFITS_REGISTRY[outfit_id]
                if outfit.category == OUTFIT_CATEGORY_WEAPONS:
                    # Create a copy of the weapon for this ship using the clone method
                    weapon_copy = outfit.clone()
                    self.weapons.append(weapon_copy)
                    print(f"AI Ship: Installed {outfit.name} on {self.ship_type}")
        except Exception as e:
            print(f"AI Ship: Failed to install outfit {outfit_id}: {e}")

    def load_ammo(self, ammo_id):
        """Load ammunition for weapons that use ammo."""
        try:
            from game_objects.standardized_outfits import OUTFITS_REGISTRY
            if ammo_id in OUTFITS_REGISTRY:
                ammo = OUTFITS_REGISTRY[ammo_id]
                # Find weapons that can use this ammo
                for weapon in self.weapons:
                    if weapon.uses_ammo and weapon.ammo_type == ammo.ammo_type:
                        weapon.current_ammo = weapon.max_ammo
                        print(f"AI Ship: Loaded {ammo.name} for {weapon.name}")
        except Exception as e:
            print(f"AI Ship: Failed to load ammo {ammo_id}: {e}")

    def rotate_image(self):
        """Update the ship's image based on its current angle."""
        old_center = self.rect.center

        try:
            if self.spritesheet and self.spritesheet.image:
                frame = self.spritesheet.get_frame_by_angle(self.angle)
                if frame:
                    self.image = frame
                else:
                    self.image = pg.transform.rotate(self.image_orig, -self.angle)
            else:
                self.image = pg.transform.rotate(self.image_orig, -self.angle)
        except Exception as e:
            print(f"AI Ship: Error during rotation: {e}")
            self.image = self.image_orig.copy()

        self.rect = self.image.get_rect()
        self.rect.center = old_center

    def apply_movement(self):
        """Apply physics to ship movement."""
        # Apply drag
        if self.vel.length_squared() > 0:
            drag = self.vel.normalize() * AI_DRAG_FACTOR * self.vel.length_squared()
            if drag.length_squared() < self.vel.length_squared():
                self.vel -= drag
            else:
                self.vel = pg.math.Vector2(0,0)

        # Limit speed
        if self.vel.length_squared() > self.max_speed * self.max_speed:
            self.vel.scale_to_length(self.max_speed)

        self.pos += self.vel
        self.rect.center = self.pos

        # World bounds (wrap around)
        if self.pos.x > self.game.camera.width: self.pos.x = 0
        if self.pos.x < 0: self.pos.x = self.game.camera.width
        if self.pos.y > self.game.camera.height: self.pos.y = 0
        if self.pos.y < 0: self.pos.y = self.game.camera.height
        self.rect.center = self.pos

    def get_angle_to_point(self, target_pos):
        """Calculate angle to a target position."""
        direction = target_pos - self.pos
        return math.degrees(math.atan2(direction.y, direction.x)) + 90

    def turn_toward_angle(self, target_angle):
        """Turn the ship toward a target angle."""
        angle_diff = (target_angle - self.angle) % 360
        if angle_diff > 180:
            angle_diff -= 360

        turn_amount = min(abs(angle_diff), self.turn_rate)
        if angle_diff > 0:
            self.angle += turn_amount
        else:
            self.angle -= turn_amount

        self.angle = self.angle % 360
        return abs(angle_diff) < self.turn_rate

    def apply_thrust_in_facing_direction(self, thrust_multiplier=1.0):
        """Apply thrust in the direction the ship is facing."""
        thrust_angle = math.radians(self.angle - 90)
        thrust_vector = pg.math.Vector2(
            math.cos(thrust_angle),
            math.sin(thrust_angle)
        ) * self.acceleration * thrust_multiplier
        self.vel += thrust_vector
