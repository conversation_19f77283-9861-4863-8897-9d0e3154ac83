"""
Weapons Editor for the Enhanced Content Editor
Handles editing of weapon outfits
"""

import tkinter as tk
from tkinter import ttk, messagebox
from .base_editor import BaseOutfitEditor

class WeaponsEditor(BaseOutfitEditor):
    """Editor for weapon outfits."""
    
    def __init__(self, parent, data_manager):
        super().__init__(parent, data_manager, "weapons")
    
    def setup_editor_ui(self, parent):
        """Setup the weapon editor interface."""
        # Basic Properties
        basic_frame = ttk.LabelFrame(parent, text="Basic Properties")
        basic_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Name and Cost
        row1 = ttk.Frame(basic_frame)
        row1.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(row1, text="Name:").grid(row=0, column=0, sticky=tk.W)
        self.name_var = tk.StringVar()
        ttk.Entry(row1, textvariable=self.name_var, width=25).grid(row=0, column=1, padx=5)
        
        ttk.Label(row1, text="Cost:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.cost_var = tk.IntVar()
        ttk.Spinbox(row1, from_=100, to=100000, textvariable=self.cost_var, width=10).grid(row=0, column=3, padx=5)
        
        # Space and Mount Type
        row2 = ttk.Frame(basic_frame)
        row2.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(row2, text="Space Required:").grid(row=0, column=0, sticky=tk.W)
        self.space_var = tk.IntVar()
        ttk.Spinbox(row2, from_=1, to=50, textvariable=self.space_var, width=10).grid(row=0, column=1, padx=5)
        
        ttk.Label(row2, text="Mount Type:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.mount_var = tk.StringVar()
        ttk.Combobox(row2, textvariable=self.mount_var, values=["fixed", "turret"], width=15).grid(row=0, column=3, padx=5)
        
        # Tech Level and Image
        row3 = ttk.Frame(basic_frame)
        row3.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(row3, text="Tech Level:").grid(row=0, column=0, sticky=tk.W)
        self.tech_level_var = tk.IntVar()
        ttk.Spinbox(row3, from_=1, to=10, textvariable=self.tech_level_var, width=10).grid(row=0, column=1, padx=5)
        
        ttk.Label(row3, text="Image Path:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.image_var = tk.StringVar()
        ttk.Entry(row3, textvariable=self.image_var, width=25).grid(row=0, column=3, padx=5)
        ttk.Button(row3, text="Browse", command=lambda: self.browse_image(self.image_var)).grid(row=0, column=4, padx=2)
        
        # Combat Properties
        combat_frame = ttk.LabelFrame(parent, text="Combat Properties")
        combat_frame.pack(fill=tk.X, padx=5, pady=5)
        
        combat_grid = ttk.Frame(combat_frame)
        combat_grid.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(combat_grid, text="Damage:").grid(row=0, column=0, sticky=tk.W)
        self.damage_var = tk.IntVar()
        ttk.Spinbox(combat_grid, from_=1, to=1000, textvariable=self.damage_var, width=10).grid(row=0, column=1, padx=5)
        
        ttk.Label(combat_grid, text="Fire Rate:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.fire_rate_var = tk.DoubleVar()
        ttk.Spinbox(combat_grid, from_=0.1, to=20.0, increment=0.1, textvariable=self.fire_rate_var, width=10).grid(row=0, column=3, padx=5)
        
        # Range and Energy
        ttk.Label(combat_grid, text="Range:").grid(row=1, column=0, sticky=tk.W)
        self.range_var = tk.IntVar()
        ttk.Spinbox(combat_grid, from_=50, to=2000, textvariable=self.range_var, width=10).grid(row=1, column=1, padx=5)
        
        ttk.Label(combat_grid, text="Energy Usage:").grid(row=1, column=2, sticky=tk.W, padx=(10, 0))
        self.energy_var = tk.IntVar()
        ttk.Spinbox(combat_grid, from_=0, to=200, textvariable=self.energy_var, width=10).grid(row=1, column=3, padx=5)
        
        # Launcher Properties
        self.launcher_frame = ttk.LabelFrame(parent, text="Launcher Properties (for ammo-using weapons)")
        
        launcher_grid = ttk.Frame(self.launcher_frame)
        launcher_grid.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(launcher_grid, text="Uses Ammo:").grid(row=0, column=0, sticky=tk.W)
        self.uses_ammo_var = tk.BooleanVar()
        ttk.Checkbutton(launcher_grid, variable=self.uses_ammo_var,
                       command=self.on_ammo_toggle).grid(row=0, column=1, padx=5, sticky=tk.W)
        
        ttk.Label(launcher_grid, text="Ammo Type:").grid(row=1, column=0, sticky=tk.W)
        self.ammo_type_var = tk.StringVar()
        ttk.Combobox(launcher_grid, textvariable=self.ammo_type_var,
                    values=["light_missile", "heavy_missile", "torpedo", "rocket"]).grid(row=1, column=1, padx=5)
        
        ttk.Label(launcher_grid, text="Max Ammo:").grid(row=1, column=2, sticky=tk.W, padx=(10, 0))
        self.max_ammo_var = tk.IntVar()
        ttk.Spinbox(launcher_grid, from_=1, to=100, textvariable=self.max_ammo_var, width=10).grid(row=1, column=3, padx=5)
        
        # Save button
        save_frame = ttk.Frame(parent)
        save_frame.pack(fill=tk.X, padx=5, pady=10)
        
        ttk.Button(save_frame, text="Save Weapon", command=self.save_item).pack(side=tk.RIGHT, padx=5)
    
    def on_ammo_toggle(self):
        """Show/hide launcher frame based on uses_ammo checkbox."""
        if self.uses_ammo_var.get():
            self.launcher_frame.pack(fill=tk.X, padx=5, pady=5)
        else:
            self.launcher_frame.pack_forget()
    
    def load_item_into_editor(self, weapon):
        """Load weapon data into the editor."""
        super().load_item_into_editor(weapon)
        
        # Load basic properties
        self.name_var.set(weapon.name)
        self.cost_var.set(getattr(weapon, 'cost', 1000))
        self.space_var.set(getattr(weapon, 'space_required', 1))
        self.mount_var.set(getattr(weapon, 'mount_type', 'fixed'))
        
        # Load tech level and image
        self.tech_level_var.set(getattr(weapon, 'min_tech_level', 1))
        self.image_var.set(getattr(weapon, 'outfitter_icon', ''))
        
        # Load combat properties
        self.damage_var.set(getattr(weapon, 'damage', 10))
        self.fire_rate_var.set(getattr(weapon, 'fire_rate', 1.0))
        self.range_var.set(getattr(weapon, 'range', 300))
        self.energy_var.set(getattr(weapon, 'energy_usage', 5))
        
        # Load launcher properties
        self.uses_ammo_var.set(getattr(weapon, 'uses_ammo', False))
        self.ammo_type_var.set(getattr(weapon, 'ammo_type', ''))
        self.max_ammo_var.set(getattr(weapon, 'max_ammo', 0))
        
        # Update UI visibility
        self.on_ammo_toggle()
    
    def save_item(self):
        """Save the current weapon with editor values."""
        if not self.current_outfit:
            messagebox.showwarning("Warning", "No weapon selected to save")
            return
        
        try:
            # Update basic properties
            self.current_outfit.name = self.name_var.get()
            if hasattr(self.current_outfit, 'cost'):
                self.current_outfit.cost = self.cost_var.get()
            if hasattr(self.current_outfit, 'space_required'):
                self.current_outfit.space_required = self.space_var.get()
            if hasattr(self.current_outfit, 'mount_type'):
                self.current_outfit.mount_type = self.mount_var.get()
            if hasattr(self.current_outfit, 'min_tech_level'):
                self.current_outfit.min_tech_level = self.tech_level_var.get()
            if hasattr(self.current_outfit, 'outfitter_icon'):
                self.current_outfit.outfitter_icon = self.image_var.get()
            
            # Update combat properties
            if hasattr(self.current_outfit, 'damage'):
                self.current_outfit.damage = self.damage_var.get()
            if hasattr(self.current_outfit, 'fire_rate'):
                self.current_outfit.fire_rate = self.fire_rate_var.get()
            if hasattr(self.current_outfit, 'range'):
                self.current_outfit.range = self.range_var.get()
            if hasattr(self.current_outfit, 'energy_usage'):
                self.current_outfit.energy_usage = self.energy_var.get()
            
            # Update launcher properties
            if hasattr(self.current_outfit, 'uses_ammo'):
                self.current_outfit.uses_ammo = self.uses_ammo_var.get()
            if hasattr(self.current_outfit, 'ammo_type'):
                self.current_outfit.ammo_type = self.ammo_type_var.get()
            if hasattr(self.current_outfit, 'max_ammo'):
                self.current_outfit.max_ammo = self.max_ammo_var.get()
            
            super().save_item()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save weapon: {e}")
    
    def create_new_item_instance(self, item_id):
        """Create a new weapon instance."""
        try:
            # Try to use the standardized system if available
            import sys
            if 'game_objects.standardized_outfits' in sys.modules:
                from game_objects.standardized_outfits import WeaponOutfit
                return WeaponOutfit(item_id, item_id.replace('_', ' ').title(), "energy")
            else:
                # Create a simple object if classes aren't available
                class SimpleWeapon:
                    def __init__(self, id, name):
                        self.id = id
                        self.name = name
                        self.category = "weapons"
                        self.subcategory = "energy"
                        self.cost = 1000
                        self.space_required = 1
                        self.mount_type = "fixed"
                        self.damage = 10
                        self.fire_rate = 1.0
                        self.range = 300
                        self.energy_usage = 5
                        self.uses_ammo = False
                        self.ammo_type = ""
                        self.max_ammo = 0
                
                return SimpleWeapon(item_id, item_id.replace('_', ' ').title())
        except ImportError:
            # Fallback to simple object
            class SimpleWeapon:
                def __init__(self, id, name):
                    self.id = id
                    self.name = name
                    self.category = "weapons"
                    self.subcategory = "energy"
                    self.cost = 1000
                    self.space_required = 1
                    self.mount_type = "fixed"
                    self.damage = 10
                    self.fire_rate = 1.0
                    self.range = 300
                    self.energy_usage = 5
                    self.uses_ammo = False
                    self.ammo_type = ""
                    self.max_ammo = 0
            
            return SimpleWeapon(item_id, item_id.replace('_', ' ').title())
