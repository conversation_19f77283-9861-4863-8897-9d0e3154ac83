{"laser_cannon": {"id": "laser_cannon", "name": "Laser Cannon", "category": "weapons", "subcategory": "energy", "cost": 1350, "space_required": 2, "min_tech_level": 1, "outfitter_icon": "", "description": "Basic energy weapon. Reliable and efficient.", "mount_type": "fixed", "damage": 15, "fire_rate": 3.0, "range": 400, "energy_usage": 8, "uses_ammo": false, "ammo_type": "", "max_ammo": 0}, "pulse_laser": {"id": "pulse_laser", "name": "<PERSON>ulse Laser", "category": "weapons", "subcategory": "energy", "cost": 1500, "space_required": 1, "min_tech_level": 1, "outfitter_icon": "", "description": "Fast-firing laser for rapid engagement.", "mount_type": "fixed", "damage": 8, "fire_rate": 6.0, "range": 350, "energy_usage": 5, "uses_ammo": false, "ammo_type": "", "max_ammo": 0}, "heavy_laser": {"id": "heavy_laser", "name": "Heavy Laser", "category": "weapons", "subcategory": "energy", "cost": 8000, "space_required": 3, "min_tech_level": 1, "outfitter_icon": "", "description": "High-powered laser cannon for capital ships.", "mount_type": "fixed", "damage": 35, "fire_rate": 1.2, "range": 450, "energy_usage": 20, "uses_ammo": false, "ammo_type": "", "max_ammo": 0}, "laser_turret": {"id": "laser_turret", "name": "<PERSON><PERSON>", "category": "weapons", "subcategory": "energy", "cost": 5000, "space_required": 2, "min_tech_level": 1, "outfitter_icon": "", "description": "Automated laser turret with 360° firing arc.", "mount_type": "turret", "damage": 12, "fire_rate": 2.5, "range": 380, "energy_usage": 10, "uses_ammo": false, "ammo_type": "", "max_ammo": 0}, "beam_laser": {"id": "beam_laser", "name": "<PERSON><PERSON>", "category": "weapons", "subcategory": "beam", "cost": 6000, "space_required": 2, "min_tech_level": 1, "outfitter_icon": "", "description": "Continuous beam weapon for sustained damage.", "mount_type": "fixed", "damage": 25, "fire_rate": 1.0, "range": 300, "energy_usage": 15, "uses_ammo": false, "ammo_type": "", "max_ammo": 0}, "missile_rack": {"id": "missile_rack", "name": "Missile Rack", "category": "weapons", "subcategory": "launcher", "cost": 3000, "space_required": 2, "min_tech_level": 1, "outfitter_icon": "", "description": "Launches light missiles for versatile combat.", "mount_type": "fixed", "damage": 0, "fire_rate": 1.5, "range": 600, "energy_usage": 2, "uses_ammo": true, "ammo_type": "light_missile", "max_ammo": 20}, "heavy_launcher": {"id": "heavy_launcher", "name": "Heavy Missile Launcher", "category": "weapons", "subcategory": "launcher", "cost": 8000, "space_required": 4, "min_tech_level": 1, "outfitter_icon": "", "description": "Launches heavy missiles for maximum damage.", "mount_type": "fixed", "damage": 0, "fire_rate": 0.8, "range": 800, "energy_usage": 5, "uses_ammo": true, "ammo_type": "heavy_missile", "max_ammo": 10}, "torpedo_tube": {"id": "torpedo_tube", "name": "<PERSON><PERSON><PERSON>", "category": "weapons", "subcategory": "launcher", "cost": 15000, "space_required": 6, "min_tech_level": 1, "outfitter_icon": "", "description": "Capital ship weapon for launching heavy torpedoes.", "mount_type": "fixed", "damage": 0, "fire_rate": 0.5, "range": 1000, "energy_usage": 8, "uses_ammo": true, "ammo_type": "torpedo", "max_ammo": 5}, "light_missile": {"id": "light_missile", "name": "Light Missile", "category": "ammunition", "subcategory": "", "cost": 150, "space_required": 1, "min_tech_level": 1, "outfitter_icon": "", "description": "Basic unguided missile.", "ammo_type": "light_missile", "quantity": 10, "damage": 40, "projectile_speed": 8, "projectile_behavior": "dumbfire", "tracking_strength": 0.0, "explosion_radius": 15}, "guided_missile": {"id": "guided_missile", "name": "Guided Missile", "category": "ammunition", "subcategory": "", "cost": 300, "space_required": 1, "min_tech_level": 1, "outfitter_icon": "", "description": "Missile with basic guidance system.", "ammo_type": "light_missile", "quantity": 8, "damage": 35, "projectile_speed": 6, "projectile_behavior": "guided", "tracking_strength": 0.6, "explosion_radius": 15}, "smart_missile": {"id": "smart_missile", "name": "Smart Missile", "category": "ammunition", "subcategory": "", "cost": 500, "space_required": 1, "min_tech_level": 1, "outfitter_icon": "", "description": "Advanced missile with superior tracking.", "ammo_type": "light_missile", "quantity": 6, "damage": 30, "projectile_speed": 5, "projectile_behavior": "guided", "tracking_strength": 0.9, "explosion_radius": 15}, "heavy_missile": {"id": "heavy_missile", "name": "Heavy Missile", "category": "ammunition", "subcategory": "", "cost": 800, "space_required": 1, "min_tech_level": 1, "outfitter_icon": "", "description": "Large unguided missile with massive warhead.", "ammo_type": "heavy_missile", "quantity": 5, "damage": 100, "projectile_speed": 6, "projectile_behavior": "dumbfire", "tracking_strength": 0.0, "explosion_radius": 30}, "torpedo": {"id": "torpedo", "name": "Torped<PERSON>", "category": "ammunition", "subcategory": "", "cost": 2000, "space_required": 1, "min_tech_level": 1, "outfitter_icon": "", "description": "Heavy guided torpedo for capital ship combat.", "ammo_type": "torpedo", "quantity": 2, "damage": 250, "projectile_speed": 4, "projectile_behavior": "guided", "tracking_strength": 0.7, "explosion_radius": 50}, "basic_shield": {"id": "basic_shield", "name": "Basic Shield Generator", "category": "defense", "subcategory": "shields", "cost": 1500, "space_required": 1, "min_tech_level": 1, "outfitter_icon": "", "description": "Standard shield generator for small ships.", "shield_boost": 50, "armor_boost": 0, "shield_recharge_boost": 1.0, "damage_reduction": 0.0}, "advanced_shield": {"id": "advanced_shield", "name": "Advanced Shield Generator", "category": "defense", "subcategory": "shields", "cost": 4000, "space_required": 2, "min_tech_level": 3, "outfitter_icon": "", "description": "High-capacity shield generator.", "shield_boost": 100, "armor_boost": 0, "shield_recharge_boost": 2.0, "damage_reduction": 0.0}, "armor_plating": {"id": "armor_plating", "name": "Armor Plating", "category": "defense", "subcategory": "armor", "cost": 1200, "space_required": 2, "min_tech_level": 2, "outfitter_icon": "assets/armor_plating.png", "description": "Additional armor plating for improved survivability.", "shield_boost": 0, "armor_boost": 30, "shield_recharge_boost": 0.0, "damage_reduction": 0.05}, "afterburner": {"id": "afterburner", "name": "Afterburner", "category": "engines", "subcategory": "afterburner", "cost": 3000, "space_required": 2, "min_tech_level": 1, "outfitter_icon": "", "description": "Provides temporary speed boost at high energy cost."}, "enhanced_thrusters": {"id": "enhanced_thrusters", "name": "Enhanced Thrusters", "category": "engines", "subcategory": "thruster", "cost": 2500, "space_required": 2, "min_tech_level": 1, "outfitter_icon": "", "description": "Improved thruster system for better acceleration."}, "maneuvering_jets": {"id": "maneuvering_jets", "name": "Maneuvering Jets", "category": "engines", "subcategory": "steering", "cost": 1800, "space_required": 1, "min_tech_level": 1, "outfitter_icon": "", "description": "Auxiliary thrusters for improved maneuverability."}, "basic_sensors": {"id": "basic_sensors", "name": "Basic Sensor Array", "category": "electronics", "subcategory": "sensors", "cost": 800, "space_required": 1, "min_tech_level": 1, "outfitter_icon": "", "description": "Improved sensor range for better detection of ships and objects."}, "targeting_computer": {"id": "targeting_computer", "name": "Targeting Computer", "category": "electronics", "subcategory": "targeting", "cost": 1200, "space_required": 1, "min_tech_level": 1, "outfitter_icon": "", "description": "Advanced targeting system that improves weapon accuracy."}, "ecm_system": {"id": "ecm_system", "name": "ECM System", "category": "electronics", "subcategory": "jamming", "cost": 2000, "space_required": 2, "min_tech_level": 1, "outfitter_icon": "", "description": "Electronic countermeasures to disrupt enemy targeting."}, "cargo_pod": {"id": "cargo_pod", "name": "Cargo Pod", "category": "utility", "subcategory": "cargo", "cost": 500, "space_required": 1, "min_tech_level": 1, "outfitter_icon": "", "description": "Additional cargo storage compartment."}, "fuel_tank": {"id": "fuel_tank", "name": "Fuel Tank", "category": "utility", "subcategory": "fuel", "cost": 300, "space_required": 1, "min_tech_level": 1, "outfitter_icon": "", "description": "Extended fuel storage for long-range travel."}, "energy_generator": {"id": "energy_generator", "name": "Energy Generator", "category": "utility", "subcategory": "power", "cost": 1500, "space_required": 2, "min_tech_level": 1, "outfitter_icon": "", "description": "Generates additional energy for ship systems."}, "crew_quarters": {"id": "crew_quarters", "name": "Crew Quarters", "category": "utility", "subcategory": "crew", "cost": 800, "space_required": 3, "min_tech_level": 1, "outfitter_icon": "", "description": "Additional crew quarters for larger crew capacity."}}