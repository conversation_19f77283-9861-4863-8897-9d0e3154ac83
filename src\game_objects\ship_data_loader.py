"""
Ship Data Loader for Escape Velocity Py
Loads ship definitions from JSON files instead of hardcoded Python objects.
The game engine defines the rules, the data files define the content.
"""

import json
import os
# Simple Ship class to avoid circular imports
class SimpleShip:
    """Simple ship class for loading from JSON without circular imports."""
    def __init__(self, name, ship_class, size, outfit_space, cargo_space,
                 turn_rate, acceleration, max_speed, shields, armor,
                 min_tech_level=1, description=""):
        self.name = name
        self.ship_class = ship_class
        self.size = size
        self.outfit_space = outfit_space
        self.cargo_space = cargo_space
        self.turn_rate = turn_rate
        self.acceleration = acceleration
        self.max_speed = max_speed
        self.shields = shields
        self.max_shields = shields
        self.armor = armor
        self.max_armor = armor
        self.min_tech_level = min_tech_level
        self.description = description
        self.cost = 10000  # Default cost
        self.image = None  # Will be set when needed
        self.image_path = None
        
        # NEW: Power and Fuel Systems (same as Ship class)
        self.power_capacity = self._calculate_base_power()
        self.power = self.power_capacity  # Start at full power
        self.power_regen_rate = self._calculate_power_regen()
        
        self.fuel_capacity = self._calculate_base_fuel()
        self.fuel = self.fuel_capacity  # Start with full fuel
        
        # Power consumption rates (per second or per use)
        self.thruster_power_cost = 5.0  # Power per second when thrusting
        self.weapon_power_cost_base = 10.0  # Base weapon power cost
        self.shield_regen_power_cost = 2.0  # Power per second for shield regen
        
    def create_default_image(self):
        """Placeholder for image creation - will be handled by the game."""
        pass
    
    def _calculate_base_power(self):
        """Calculate base power capacity based on ship size and class."""
        size_multipliers = {
            "small": 60,
            "medium": 100, 
            "large": 150,
            "capital": 200
        }
        
        class_modifiers = {
            "fighter": 1.0,
            "freighter": 0.8,  # Less power for cargo ships
            "transport": 0.8,
            "corvette": 1.1,
            "frigate": 1.2,
            "cruiser": 1.3,
            "destroyer": 1.2,
            "carrier": 1.4,
            "battleship": 1.5,
            "dreadnought": 1.6
        }
        
        base_power = size_multipliers.get(self.size, 100)
        class_modifier = class_modifiers.get(self.ship_class, 1.0)
        
        return int(base_power * class_modifier)
    
    def _calculate_power_regen(self):
        """Calculate power regeneration rate per second."""
        # Base regen based on ship size
        size_regen = {
            "small": 3.0,
            "medium": 4.0,
            "large": 5.0,
            "capital": 6.0
        }
        
        return size_regen.get(self.size, 4.0)
    
    def _calculate_base_fuel(self):
        """Calculate base fuel capacity based on ship size and class."""
        size_base = {
            "small": 100,
            "medium": 150,
            "large": 200,
            "capital": 300
        }
        
        class_modifiers = {
            "fighter": 0.8,  # Fighters have less fuel range
            "freighter": 1.3,  # Cargo ships need long range
            "transport": 1.2,
            "corvette": 1.0,
            "frigate": 1.1,
            "cruiser": 1.2,
            "destroyer": 1.0,
            "carrier": 1.4,  # Carriers need long deployment range
            "battleship": 1.1,
            "dreadnought": 1.0  # Massive but not necessarily long-range
        }
        
        base_fuel = size_base.get(self.size, 150)
        class_modifier = class_modifiers.get(self.ship_class, 1.0)
        
        return int(base_fuel * class_modifier)
    
    def consume_power(self, amount):
        """Consume power, returns True if successful, False if insufficient power."""
        if self.power >= amount:
            self.power = max(0, self.power - amount)
            return True
        return False
    
    def consume_fuel(self, amount):
        """Consume fuel, returns True if successful, False if insufficient fuel."""
        if self.fuel >= amount:
            self.fuel = max(0, self.fuel - amount)
            return True
        return False
    
    def regenerate_power(self, dt):
        """Regenerate power over time."""
        if self.power < self.power_capacity:
            regen_amount = self.power_regen_rate * dt
            self.power = min(self.power_capacity, self.power + regen_amount)
        
    def __str__(self):
        return f"{self.name} ({self.ship_class}, {self.size})"

# Global ship registry - this replaces the hardcoded SHIPS dict
SHIPS_REGISTRY = {}

def load_ships_from_data_files():
    """
    Load ship data from JSON files and create ship objects.
    This replaces the hardcoded ship definitions.
    """
    # Clear existing registry first
    SHIPS_REGISTRY.clear()
    
    # Look for ship data files
    data_files = ["ships_data.json"]
    
    # Try to find data files in the game root directory
    current_dir = os.path.dirname(__file__)
    game_root = os.path.dirname(os.path.dirname(current_dir))
    
    total_loaded = 0
    
    for data_file in data_files:
        file_path = os.path.join(game_root, data_file)
        if os.path.exists(file_path):
            loaded_count = load_ships_from_file(file_path)
            total_loaded += loaded_count
            print(f"Loaded {loaded_count} ships from {data_file}")
    
    # If no data files found, return 0 (caller will handle creating defaults)
    if total_loaded == 0:
        print("No ship data files found")
    
    print(f"Total ships loaded: {total_loaded}")
    return total_loaded

def load_ships_from_file(file_path):
    """Load ships from a single JSON file."""
    try:
        with open(file_path, 'r') as f:
            ship_data = json.load(f)
        
        loaded_count = 0
        for ship_id, data in ship_data.items():
            ship = create_ship_from_data(ship_id, data)
            if ship:
                SHIPS_REGISTRY[ship_id] = ship
                loaded_count += 1
        
        return loaded_count
    
    except Exception as e:
        print(f"Error loading ships from {file_path}: {e}")
        return 0

def create_ship_from_data(ship_id, data):
    """Create a ship object from JSON data using the game engine rules."""
    try:
        # We'll create a simple ship object without importing from ships
        # to avoid circular imports
        
        # Basic properties
        name = data.get('name', ship_id.replace('_', ' ').title())
        ship_class = data.get('ship_class', 'fighter')
        size = data.get('size', 'small')
        outfit_space = data.get('outfit_space', 20)
        cargo_space = data.get('cargo_space', 10)
        turn_rate = data.get('turn_rate', 1.0)
        acceleration = data.get('acceleration', 0.5)
        max_speed = data.get('max_speed', 3.0)
        shields = data.get('shields', 50)
        armor = data.get('armor', 30)
        min_tech_level = data.get('min_tech_level', 1)
        description = data.get('description', '')
        
        # Create ship using the SimpleShip class
        ship = SimpleShip(
            name=name,
            ship_class=ship_class,
            size=size,
            outfit_space=outfit_space,
            cargo_space=cargo_space,
            turn_rate=turn_rate,
            acceleration=acceleration,
            max_speed=max_speed,
            shields=shields,
            armor=armor,
            min_tech_level=min_tech_level,
            description=description
        )
        
        # Add cost if provided
        if 'cost' in data:
            ship.cost = data['cost']
            
        # Add visual and animation properties
        ship.game_sprite = data.get('game_sprite', '')
        ship.shipyard_sprite = data.get('shipyard_sprite', '')
        ship.animation_frames = data.get('animation_frames', 1)
        ship.sprite_size = data.get('sprite_size', 32)
        ship.animation_type = data.get('animation_type', 'rotation')
        ship.frame_rate = data.get('frame_rate', 30)
        ship.idle_sequence = data.get('idle_sequence', '0-0')
        ship.thrust_sequence = data.get('thrust_sequence', '0-0')
        
        return ship
        
    except Exception as e:
        print(f"Error creating ship {ship_id}: {e}")
        return None

def save_current_ships_to_file(file_path="ships_data.json"):
    """
    Save current ship registry to a JSON file.
    This can be used to export the current state or create backups.
    """
    try:
        # Make sure we're saving to the game root
        if not os.path.isabs(file_path):
            current_dir = os.path.dirname(__file__)
            game_root = os.path.dirname(os.path.dirname(current_dir))
            file_path = os.path.join(game_root, file_path)
        
        ships_data = {}
        for ship_id, ship in SHIPS_REGISTRY.items():
            ship_data = {
                'name': ship.name,
                'ship_class': ship.ship_class,
                'size': ship.size,
                'outfit_space': ship.outfit_space,
                'cargo_space': ship.cargo_space,
                'turn_rate': ship.turn_rate,
                'acceleration': ship.acceleration,
                'max_speed': ship.max_speed,
                'shields': ship.shields,
                'armor': ship.armor,
                'min_tech_level': ship.min_tech_level,
                'description': ship.description,
                'cost': getattr(ship, 'cost', 10000),
                'game_sprite': getattr(ship, 'game_sprite', ''),
                'shipyard_sprite': getattr(ship, 'shipyard_sprite', ''),
                'animation_frames': getattr(ship, 'animation_frames', 1),
                'sprite_size': getattr(ship, 'sprite_size', 32),
                'animation_type': getattr(ship, 'animation_type', 'rotation'),
                'frame_rate': getattr(ship, 'frame_rate', 30),
                'idle_sequence': getattr(ship, 'idle_sequence', '0-0'),
                'thrust_sequence': getattr(ship, 'thrust_sequence', '0-0')
            }
            ships_data[ship_id] = ship_data

        with open(file_path, 'w') as f:
            json.dump(ships_data, f, indent=2)

        print(f"Saved {len(ships_data)} ships to {file_path}")
        return True

    except Exception as e:
        print(f"Failed to save ships to {file_path}: {e}")
        return False

# Compatibility functions for the game
def get_ship_by_id(ship_id):
    """Get a ship by its ID."""
    return SHIPS_REGISTRY.get(ship_id)

def get_ship_by_name(name):
    """Get a ship by its name (case insensitive)."""
    for ship in SHIPS_REGISTRY.values():
        if ship.name.lower() == name.lower():
            return ship
    return None

def get_ships_by_class(ship_class):
    """Get all ships of a specific class."""
    return [ship for ship in SHIPS_REGISTRY.values() if ship.ship_class == ship_class]

def get_ships_by_size(size):
    """Get all ships of a specific size."""
    return [ship for ship in SHIPS_REGISTRY.values() if ship.size == size]
