"""
Engines Editor for the Enhanced Content Editor
Handles editing of engine outfits
"""

import tkinter as tk
from tkinter import ttk, messagebox
from .base_editor import BaseOutfitEditor

class EnginesEditor(BaseOutfitEditor):
    """Editor for engine outfits."""
    
    def __init__(self, parent, data_manager):
        super().__init__(parent, data_manager, "engines")
    
    def setup_editor_ui(self, parent):
        """Setup the engines editor interface."""
        # Basic Properties
        basic_frame = ttk.LabelFrame(parent, text="Basic Properties")
        basic_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Name and Cost
        row1 = ttk.Frame(basic_frame)
        row1.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(row1, text="Name:").grid(row=0, column=0, sticky=tk.W)
        self.name_var = tk.StringVar()
        ttk.Entry(row1, textvariable=self.name_var, width=25).grid(row=0, column=1, padx=5)
        
        ttk.Label(row1, text="Cost:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.cost_var = tk.IntVar()
        ttk.Spinbox(row1, from_=100, to=100000, textvariable=self.cost_var, width=10).grid(row=0, column=3, padx=5)
        
        # Space and Type
        row2 = ttk.Frame(basic_frame)
        row2.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(row2, text="Space Required:").grid(row=0, column=0, sticky=tk.W)
        self.space_var = tk.IntVar()
        ttk.Spinbox(row2, from_=1, to=20, textvariable=self.space_var, width=10).grid(row=0, column=1, padx=5)
        
        ttk.Label(row2, text="Type:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.type_var = tk.StringVar()
        ttk.Combobox(row2, textvariable=self.type_var, 
                    values=["thruster", "afterburner", "steering"], width=15).grid(row=0, column=3, padx=5)
        
        # Image Path
        row3 = ttk.Frame(basic_frame)
        row3.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(row3, text="Image Path:").grid(row=0, column=0, sticky=tk.W)
        self.image_var = tk.StringVar()
        ttk.Entry(row3, textvariable=self.image_var, width=35).grid(row=0, column=1, padx=5)
        ttk.Button(row3, text="Browse", command=lambda: self.browse_image(self.image_var)).grid(row=0, column=2, padx=2)
        
        # Engine Properties
        engine_frame = ttk.LabelFrame(parent, text="Engine Properties")
        engine_frame.pack(fill=tk.X, padx=5, pady=5)
        
        engine_grid = ttk.Frame(engine_frame)
        engine_grid.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(engine_grid, text="Acceleration Boost:").grid(row=0, column=0, sticky=tk.W)
        self.acceleration_var = tk.DoubleVar()
        ttk.Spinbox(engine_grid, from_=0.0, to=10.0, increment=0.1, textvariable=self.acceleration_var, width=10).grid(row=0, column=1, padx=5)
        
        ttk.Label(engine_grid, text="Max Speed Boost:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.max_speed_var = tk.DoubleVar()
        ttk.Spinbox(engine_grid, from_=0.0, to=10.0, increment=0.1, textvariable=self.max_speed_var, width=10).grid(row=0, column=3, padx=5)
        
        ttk.Label(engine_grid, text="Turn Rate Boost:").grid(row=1, column=0, sticky=tk.W)
        self.turn_rate_var = tk.DoubleVar()
        ttk.Spinbox(engine_grid, from_=0.0, to=5.0, increment=0.1, textvariable=self.turn_rate_var, width=10).grid(row=1, column=1, padx=5)
        
        ttk.Label(engine_grid, text="Energy Drain:").grid(row=1, column=2, sticky=tk.W, padx=(10, 0))
        self.energy_drain_var = tk.DoubleVar()
        ttk.Spinbox(engine_grid, from_=0.0, to=50.0, increment=0.1, textvariable=self.energy_drain_var, width=10).grid(row=1, column=3, padx=5)
        
        # Save button
        save_frame = ttk.Frame(parent)
        save_frame.pack(fill=tk.X, padx=5, pady=10)
        
        ttk.Button(save_frame, text="Save Engine", command=self.save_item).pack(side=tk.RIGHT, padx=5)
    
    def load_item_into_editor(self, engine):
        """Load engine data into the editor."""
        super().load_item_into_editor(engine)
        
        self.name_var.set(engine.name)
        self.cost_var.set(getattr(engine, 'cost', 1000))
        self.space_var.set(getattr(engine, 'space_required', 1))
        self.type_var.set(getattr(engine, 'subcategory', 'thruster'))
        self.acceleration_var.set(getattr(engine, 'acceleration_boost', 0.0))
        self.max_speed_var.set(getattr(engine, 'max_speed_boost', 0.0))
        self.turn_rate_var.set(getattr(engine, 'turn_rate_boost', 0.0))
        self.energy_drain_var.set(getattr(engine, 'energy_drain', 0.0))
        self.image_var.set(getattr(engine, 'outfitter_icon', ''))
    
    def save_item(self):
        """Save the current engine with editor values."""
        if not self.current_outfit:
            messagebox.showwarning("Warning", "No engine selected to save")
            return
        
        try:
            self.current_outfit.name = self.name_var.get()
            if hasattr(self.current_outfit, 'cost'):
                self.current_outfit.cost = self.cost_var.get()
            if hasattr(self.current_outfit, 'space_required'):
                self.current_outfit.space_required = self.space_var.get()
            if hasattr(self.current_outfit, 'subcategory'):
                self.current_outfit.subcategory = self.type_var.get()
            if hasattr(self.current_outfit, 'acceleration_boost'):
                self.current_outfit.acceleration_boost = self.acceleration_var.get()
            if hasattr(self.current_outfit, 'max_speed_boost'):
                self.current_outfit.max_speed_boost = self.max_speed_var.get()
            if hasattr(self.current_outfit, 'turn_rate_boost'):
                self.current_outfit.turn_rate_boost = self.turn_rate_var.get()
            if hasattr(self.current_outfit, 'energy_drain'):
                self.current_outfit.energy_drain = self.energy_drain_var.get()
            if hasattr(self.current_outfit, 'outfitter_icon'):
                self.current_outfit.outfitter_icon = self.image_var.get()
            
            super().save_item()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save engine: {e}")
    
    def create_new_item_instance(self, item_id):
        """Create a new engine instance."""
        class SimpleEngine:
            def __init__(self, id, name):
                self.id = id
                self.name = name
                self.category = "engines"
                self.subcategory = "thruster"
                self.cost = 1000
                self.space_required = 1
                self.acceleration_boost = 0.0
                self.max_speed_boost = 0.0
                self.turn_rate_boost = 0.0
                self.energy_drain = 0.0
                self.outfitter_icon = ""
        
        return SimpleEngine(item_id, item_id.replace('_', ' ').title())
