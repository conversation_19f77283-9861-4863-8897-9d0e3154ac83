"""
Ship definitions for Escape Velocity Py.
This module contains the base Ship class and all ship types available in the game.
"""

import pygame as pg
import os
import math

# Ship size categories
SIZE_SMALL = "small"
SIZE_MEDIUM = "medium"
SIZE_LARGE = "large"
SIZE_CAPITAL = "capital"

# Ship classes/types
CLASS_FIGHTER = "fighter"
CLASS_FREIGHTER = "freighter"
CLASS_TRANSPORT = "transport"
CLASS_CORVETTE = "corvette"
CLASS_FRIGATE = "frigate"
CLASS_CRUISER = "cruiser"
CLASS_DESTROYER = "destroyer"
CLASS_CARRIER = "carrier"
CLASS_BATTLESHIP = "battleship"
CLASS_DREADNOUGHT = "dreadnought"

class Ship:
    """Base class for all ships in the game."""

    def __init__(self, name, ship_class, size, outfit_space, cargo_space,
                 turn_rate, acceleration, max_speed, shields, armor,
                 min_tech_level=1, description="", image_path=None):
        """
        Initialize a ship with its specifications.

        Args:
            name (str): The name of the ship
            ship_class (str): The class/type of the ship (fighter, freighter, etc.)
            size (str): Size category (small, medium, large, capital)
            outfit_space (int): Available space for outfits in tons
            cargo_space (int): Available cargo space in tons
            turn_rate (float): How quickly the ship can turn (degrees per second)
            acceleration (float): Acceleration rate
            max_speed (float): Maximum speed
            shields (int): Shield strength
            armor (int): Armor/hull strength
            min_tech_level (int, optional): Minimum tech level required to purchase this ship.
            description (str, optional): Ship description. Defaults to "".
            image_path (str, optional): Path to the ship's image. Defaults to None.
        """
        self.name = name
        self.ship_class = ship_class
        self.size = size
        self.outfit_space = outfit_space
        self.cargo_space = cargo_space
        self.turn_rate = turn_rate
        self.acceleration = acceleration
        self.max_speed = max_speed
        self.shields = shields
        self.max_shields = shields
        self.armor = armor
        self.max_armor = armor
        self.min_tech_level = min_tech_level
        self.description = description
        self.image_path = image_path
        self.image = None
        
        # NEW: Power and Fuel Systems
        self.power_capacity = self._calculate_base_power()
        self.power = self.power_capacity  # Start at full power
        self.power_regen_rate = self._calculate_power_regen()
        
        self.fuel_capacity = self._calculate_base_fuel()
        self.fuel = self.fuel_capacity  # Start with full fuel
        
        # Power consumption rates (per second or per use)
        self.thruster_power_cost = 5.0  # Power per second when thrusting
        self.weapon_power_cost_base = 10.0  # Base weapon power cost
        self.shield_regen_power_cost = 2.0  # Power per second for shield regen

        # Load the image if provided
        if image_path and os.path.exists(image_path):
            try:
                self.image = pg.image.load(image_path).convert_alpha()
            except:
                print(f"Failed to load image for {name}: {image_path}")
                # Create a default colored rectangle based on ship size
                self.create_default_image()
        else:
            self.create_default_image()

    def create_default_image(self):
        """Create a default image for the ship based on its size."""
        # Size of the ship image based on its category
        sizes = {
            SIZE_SMALL: (30, 20),
            SIZE_MEDIUM: (40, 30),
            SIZE_LARGE: (60, 40),
            SIZE_CAPITAL: (80, 60)
        }

        # Color based on ship class
        colors = {
            CLASS_FIGHTER: (255, 0, 0),       # Red
            CLASS_FREIGHTER: (0, 255, 0),     # Green
            CLASS_TRANSPORT: (0, 200, 100),   # Teal
            CLASS_CORVETTE: (255, 255, 0),    # Yellow
            CLASS_FRIGATE: (0, 0, 255),       # Blue
            CLASS_CRUISER: (255, 0, 255),     # Magenta
            CLASS_DESTROYER: (128, 0, 0),     # Dark Red
            CLASS_CARRIER: (0, 128, 0),       # Dark Green
            CLASS_BATTLESHIP: (0, 0, 128),    # Dark Blue
            CLASS_DREADNOUGHT: (128, 128, 0)  # Olive
        }

        width, height = sizes.get(self.size, (40, 30))
        color = colors.get(self.ship_class, (200, 200, 200))

        # Create a surface for the ship
        self.image = pg.Surface((width, height), pg.SRCALPHA)

        # Draw a simple ship shape
        if self.ship_class in [CLASS_FIGHTER, CLASS_CORVETTE]:
            # Triangle shape for fighters and corvettes
            points = [(0, height), (width/2, 0), (width, height)]
            pg.draw.polygon(self.image, color, points)
        else:
            # Rectangle with rounded ends for larger ships
            pg.draw.rect(self.image, color, (0, height/4, width, height/2))
            pg.draw.circle(self.image, color, (width/4, height/2), height/4)
            pg.draw.circle(self.image, color, (width*3/4, height/2), height/4)

        # Add a contrasting line to make the ship more visible
        if self.ship_class in [CLASS_FIGHTER, CLASS_CORVETTE]:
            pg.draw.line(self.image, (255, 255, 255), (width/2, 0), (width/2, height/2), 2)
        else:
            pg.draw.line(self.image, (255, 255, 255), (width/4, height/2), (width*3/4, height/2), 2)

    def _calculate_base_power(self):
        """Calculate base power capacity based on ship size and class."""
        size_multipliers = {
            SIZE_SMALL: 60,
            SIZE_MEDIUM: 100, 
            SIZE_LARGE: 150,
            SIZE_CAPITAL: 200
        }
        
        class_modifiers = {
            CLASS_FIGHTER: 1.0,
            CLASS_FREIGHTER: 0.8,  # Less power for cargo ships
            CLASS_TRANSPORT: 0.8,
            CLASS_CORVETTE: 1.1,
            CLASS_FRIGATE: 1.2,
            CLASS_CRUISER: 1.3,
            CLASS_DESTROYER: 1.2,
            CLASS_CARRIER: 1.4,
            CLASS_BATTLESHIP: 1.5,
            CLASS_DREADNOUGHT: 1.6
        }
        
        base_power = size_multipliers.get(self.size, 100)
        class_modifier = class_modifiers.get(self.ship_class, 1.0)
        
        return int(base_power * class_modifier)
    
    def _calculate_power_regen(self):
        """Calculate power regeneration rate per second."""
        # Base regen based on ship size
        size_regen = {
            SIZE_SMALL: 3.0,
            SIZE_MEDIUM: 4.0,
            SIZE_LARGE: 5.0,
            SIZE_CAPITAL: 6.0
        }
        
        return size_regen.get(self.size, 4.0)
    
    def _calculate_base_fuel(self):
        """Calculate base fuel capacity based on ship size and class."""
        size_base = {
            SIZE_SMALL: 100,
            SIZE_MEDIUM: 150,
            SIZE_LARGE: 200,
            SIZE_CAPITAL: 300
        }
        
        class_modifiers = {
            CLASS_FIGHTER: 0.8,  # Fighters have less fuel range
            CLASS_FREIGHTER: 1.3,  # Cargo ships need long range
            CLASS_TRANSPORT: 1.2,
            CLASS_CORVETTE: 1.0,
            CLASS_FRIGATE: 1.1,
            CLASS_CRUISER: 1.2,
            CLASS_DESTROYER: 1.0,
            CLASS_CARRIER: 1.4,  # Carriers need long deployment range
            CLASS_BATTLESHIP: 1.1,
            CLASS_DREADNOUGHT: 1.0  # Massive but not necessarily long-range
        }
        
        base_fuel = size_base.get(self.size, 150)
        class_modifier = class_modifiers.get(self.ship_class, 1.0)
        
        return int(base_fuel * class_modifier)
    
    def consume_power(self, amount):
        """Consume power, returns True if successful, False if insufficient power."""
        if self.power >= amount:
            self.power = max(0, self.power - amount)
            return True
        return False
    
    def consume_fuel(self, amount):
        """Consume fuel, returns True if successful, False if insufficient fuel."""
        if self.fuel >= amount:
            self.fuel = max(0, self.fuel - amount)
            return True
        return False
    
    def regenerate_power(self, dt):
        """Regenerate power over time."""
        if self.power < self.power_capacity:
            regen_amount = self.power_regen_rate * dt
            self.power = min(self.power_capacity, self.power + regen_amount)

    def __str__(self):
        return f"{self.name} ({self.ship_class}, {self.size})"


# Define all ship types
SHIPS = {
    # Small ships
    "scout": Ship(
        name="Scout",
        ship_class=CLASS_FIGHTER,
        size=SIZE_SMALL,
        outfit_space=20,
        cargo_space=5,
        turn_rate=1.75,  # Halved from 3.5
        acceleration=0.6,  # Halved from 1.2
        max_speed=4.0,  # Halved from 8.0
        shields=50,
        armor=30,
        min_tech_level=1,
        description="A fast, agile scout ship with minimal cargo space."
    ),

    "light_fighter": Ship(
        name="Light Fighter",
        ship_class=CLASS_FIGHTER,
        size=SIZE_SMALL,
        outfit_space=25,
        cargo_space=3,
        turn_rate=1.5,  # Halved from 3.0
        acceleration=0.5,  # Halved from 1.0
        max_speed=3.75,  # Halved from 7.5
        shields=60,
        armor=40,
        min_tech_level=1,
        description="A nimble fighter designed for dogfighting."
    ),

    "interceptor": Ship(
        name="Interceptor",
        ship_class=CLASS_FIGHTER,
        size=SIZE_SMALL,
        outfit_space=30,
        cargo_space=2,
        turn_rate=1.6,  # Halved from 3.2
        acceleration=0.65,  # Halved from 1.3
        max_speed=4.25,  # Halved from 8.5
        shields=55,
        armor=35,
        min_tech_level=2,
        description="A high-speed interceptor designed to catch fleeing ships."
    ),

    "courier": Ship(
        name="Courier",
        ship_class=CLASS_TRANSPORT,
        size=SIZE_SMALL,
        outfit_space=15,
        cargo_space=30,
        turn_rate=1.0,  # Halved from 2.0
        acceleration=0.4,  # Halved from 0.8
        max_speed=3.25,  # Halved from 6.5
        shields=40,
        armor=50,
        min_tech_level=1,
        description="A small, fast transport ship for delivering small cargo quickly."
    ),

    # Medium ships
    "heavy_fighter": Ship(
        name="Heavy Fighter",
        ship_class=CLASS_FIGHTER,
        size=SIZE_MEDIUM,
        outfit_space=45,
        cargo_space=8,
        turn_rate=1.1,  # Halved from 2.2
        acceleration=0.45,  # Halved from 0.9
        max_speed=3.0,  # Halved from 6.0
        shields=100,
        armor=80,
        min_tech_level=2,
        description="A well-armed and armored fighter with good combat capabilities."
    ),

    "freighter": Ship(
        name="Freighter",
        ship_class=CLASS_FREIGHTER,
        size=SIZE_MEDIUM,
        outfit_space=35,
        cargo_space=120,
        turn_rate=0.5,  # Halved from 1.0
        acceleration=0.25,  # Halved from 0.5
        max_speed=2.25,  # Halved from 4.5
        shields=80,
        armor=100,
        min_tech_level=2,
        description="A standard cargo ship with good capacity."
    ),

    "passenger_liner": Ship(
        name="Passenger Liner",
        ship_class=CLASS_TRANSPORT,
        size=SIZE_MEDIUM,
        outfit_space=40,
        cargo_space=60,
        turn_rate=0.6,  # Halved from 1.2
        acceleration=0.3,  # Halved from 0.6
        max_speed=2.5,  # Halved from 5.0
        shields=90,
        armor=70,
        min_tech_level=2,
        description="A ship designed for passenger transport with moderate cargo space."
    ),

    "gunship": Ship(
        name="Gunship",
        ship_class=CLASS_CORVETTE,
        size=SIZE_MEDIUM,
        outfit_space=60,
        cargo_space=20,
        turn_rate=0.9,  # Halved from 1.8
        acceleration=0.4,  # Halved from 0.8
        max_speed=2.75,  # Halved from 5.5
        shields=120,
        armor=100,
        min_tech_level=3,
        description="A heavily armed ship designed for combat support."
    ),

    # Large ships
    "heavy_freighter": Ship(
        name="Heavy Freighter",
        ship_class=CLASS_FREIGHTER,
        size=SIZE_LARGE,
        outfit_space=70,
        cargo_space=250,
        turn_rate=0.35,  # Halved from 0.7
        acceleration=0.15,  # Halved from 0.3
        max_speed=1.75,  # Halved from 3.5
        shields=150,
        armor=200,
        min_tech_level=3,
        description="A large cargo vessel with substantial capacity."
    ),

    "corvette": Ship(
        name="Corvette",
        ship_class=CLASS_CORVETTE,
        size=SIZE_LARGE,
        outfit_space=90,
        cargo_space=40,
        turn_rate=0.5,  # Halved from 1.0
        acceleration=0.25,  # Halved from 0.5
        max_speed=2.0,  # Halved from 4.0
        shields=180,
        armor=150,
        min_tech_level=3,
        description="A versatile warship that balances firepower, protection, and speed."
    ),

    "frigate": Ship(
        name="Frigate",
        ship_class=CLASS_FRIGATE,
        size=SIZE_LARGE,
        outfit_space=120,
        cargo_space=60,
        turn_rate=0.4,  # Halved from 0.8
        acceleration=0.2,  # Halved from 0.4
        max_speed=1.9,  # Halved from 3.8
        shields=220,
        armor=180,
        min_tech_level=4,
        description="A medium warship with good all-around capabilities."
    ),

    "bulk_carrier": Ship(
        name="Bulk Carrier",
        ship_class=CLASS_FREIGHTER,
        size=SIZE_LARGE,
        outfit_space=60,
        cargo_space=350,
        turn_rate=0.25,  # Halved from 0.5
        acceleration=0.1,  # Halved from 0.2
        max_speed=1.5,  # Halved from 3.0
        shields=130,
        armor=250,
        min_tech_level=3,
        description="A massive cargo ship designed for bulk transport."
    ),

    # Capital ships
    "cruiser": Ship(
        name="Cruiser",
        ship_class=CLASS_CRUISER,
        size=SIZE_CAPITAL,
        outfit_space=180,
        cargo_space=100,
        turn_rate=0.25,  # Halved from 0.5
        acceleration=0.15,  # Halved from 0.3
        max_speed=1.6,  # Halved from 3.2
        shields=300,
        armor=250,
        min_tech_level=4,
        description="A large warship with heavy armament and good endurance."
    ),

    "destroyer": Ship(
        name="Destroyer",
        ship_class=CLASS_DESTROYER,
        size=SIZE_CAPITAL,
        outfit_space=150,
        cargo_space=80,
        turn_rate=0.3,  # Halved from 0.6
        acceleration=0.175,  # Halved from 0.35
        max_speed=1.75,  # Halved from 3.5
        shields=280,
        armor=220,
        min_tech_level=4,
        description="A fast capital ship designed to counter smaller vessels."
    ),

    "carrier": Ship(
        name="Carrier",
        ship_class=CLASS_CARRIER,
        size=SIZE_CAPITAL,
        outfit_space=200,
        cargo_space=150,
        turn_rate=0.15,  # Halved from 0.3
        acceleration=0.1,  # Halved from 0.2
        max_speed=1.4,  # Halved from 2.8
        shields=350,
        armor=300,
        min_tech_level=5,
        description="A massive ship designed to carry and deploy fighter craft."
    ),

    "battleship": Ship(
        name="Battleship",
        ship_class=CLASS_BATTLESHIP,
        size=SIZE_CAPITAL,
        outfit_space=250,
        cargo_space=120,
        turn_rate=0.125,  # Halved from 0.25
        acceleration=0.075,  # Halved from 0.15
        max_speed=1.25,  # Halved from 2.5
        shields=400,
        armor=450,
        min_tech_level=5,
        description="A heavily armed and armored capital ship designed for fleet engagements."
    ),

    "dreadnought": Ship(
        name="Dreadnought",
        ship_class=CLASS_DREADNOUGHT,
        size=SIZE_CAPITAL,
        outfit_space=300,
        cargo_space=150,
        turn_rate=0.1,  # Halved from 0.2
        acceleration=0.05,  # Halved from 0.1
        max_speed=1.0,  # Halved from 2.0
        shields=500,
        armor=600,
        min_tech_level=5, # Assuming highest tech for dreadnoughts
        description="The largest and most powerful warship, with overwhelming firepower."
    ),

    "super_freighter": Ship(
        name="Super Freighter",
        ship_class=CLASS_FREIGHTER,
        size=SIZE_CAPITAL,
        outfit_space=100,
        cargo_space=500,
        turn_rate=0.1,  # Halved from 0.2
        acceleration=0.05,  # Halved from 0.1
        max_speed=1.1,  # Halved from 2.2
        shields=200,
        armor=350,
        min_tech_level=4,
        description="An enormous cargo vessel with unmatched cargo capacity."
    ),
}

def get_ship_by_name(name):
    """Get a ship by its name (case insensitive)."""
    for ship_id, ship in SHIPS.items():
        if ship.name.lower() == name.lower():
            return ship
    return None

def get_ship_by_id(ship_id):
    """Get a ship by its ID."""
    return SHIPS.get(ship_id)

def get_ships_by_class(ship_class):
    """Get all ships of a specific class."""
    return [ship for ship in SHIPS.values() if ship.ship_class == ship_class]

def get_ships_by_size(size):
    """Get all ships of a specific size."""
    return [ship for ship in SHIPS.values() if ship.size == size]


# Initialize ships from data files instead of hardcoded definitions
# This replaces the hardcoded ships with data-driven ships loaded from JSON
try:
    from .ship_data_loader import load_ships_from_data_files, SHIPS_REGISTRY, save_current_ships_to_file
    loaded_count = load_ships_from_data_files()
    if loaded_count > 0:
        # Successfully loaded ships from JSON - replace hardcoded ships
        print(f"Loaded {loaded_count} ships from JSON data files")
        SHIPS.clear()
        SHIPS.update(SHIPS_REGISTRY)
    else:
        # No JSON files found - create them from hardcoded ships
        print("No ship data files found - creating ships_data.json from hardcoded ships")
        # Save hardcoded ships to SHIPS_REGISTRY and create JSON file
        SHIPS_REGISTRY.clear()
        SHIPS_REGISTRY.update(SHIPS)
        if save_current_ships_to_file():
            print("Successfully created ships_data.json - restart game to use data-driven ships")
        
except ImportError as e:
    print(f"Could not import ship_data_loader: {e}")
    print("Using hardcoded ships")
except Exception as e:
    print(f"Error with ship data system: {e}")
    print("Using hardcoded ships")
