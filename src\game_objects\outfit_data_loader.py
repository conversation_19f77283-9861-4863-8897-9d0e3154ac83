"""
Outfit Data Loader for Escape Velocity Py
Loads outfit definitions from JSON files instead of hardcoded Python objects.
The game engine defines the rules, the data files define the content.
"""

import json
import os
from .standardized_outfits import *

def load_outfits_from_data_files():
    """
    Load outfit data from JSON files and create outfit objects.
    This replaces the hardcoded example outfits.
    """
    # Clear existing registry first
    OUTFITS_REGISTRY.clear()
    
    # Look for outfit data files
    data_files = ["outfits_data.json", "weapons_data.json", "ammunition_data.json", "defense_data.json"]
    
    # Try to find data files in the game root directory
    current_dir = os.path.dirname(__file__)  # src/game_objects/
    game_root = os.path.dirname(os.path.dirname(current_dir))  # Should be EscapeVelocityPy/
    
    print(f"DEBUG: current_dir = {current_dir}")
    print(f"DEBUG: game_root = {game_root}")
    
    total_loaded = 0
    
    for data_file in data_files:
        file_path = os.path.join(game_root, data_file)
        print(f"DEBUG: Looking for {data_file} at {file_path}")
        if os.path.exists(file_path):
            loaded_count = load_outfits_from_file(file_path)
            total_loaded += loaded_count
            print(f"Loaded {loaded_count} outfits from {data_file}")
        else:
            print(f"File not found: {file_path}")
    
    # If no data files found, create minimal defaults
    if total_loaded == 0:
        print("No outfit data files found, creating minimal defaults...")
        create_minimal_defaults()
        total_loaded = len(OUTFITS_REGISTRY)
    
    print(f"Total outfits loaded: {total_loaded}")
    return total_loaded

def load_outfits_from_file(file_path):
    """Load outfits from a single JSON file."""
    try:
        with open(file_path, 'r') as f:
            outfit_data = json.load(f)
        
        loaded_count = 0
        for outfit_id, data in outfit_data.items():
            outfit = create_outfit_from_data(outfit_id, data)
            if outfit:
                OUTFITS_REGISTRY[outfit_id] = outfit
                loaded_count += 1
        
        return loaded_count
    
    except Exception as e:
        print(f"Error loading outfits from {file_path}: {e}")
        return 0

def create_outfit_from_data(outfit_id, data):
    """Create an outfit object from JSON data using the game engine rules."""
    try:
        category = data.get('category', 'utility')
        name = data.get('name', outfit_id.replace('_', ' ').title())
        subcategory = data.get('subcategory', '')
        
        # Create the appropriate outfit type based on category
        if category == "weapons":
            outfit = Weapon(outfit_id, name, subcategory)
            
            # Apply weapon-specific properties
            outfit.mount_type = data.get('mount_type', MOUNT_TYPE_FIXED)
            outfit.damage = data.get('damage', 10)
            outfit.fire_rate = data.get('fire_rate', 1.0)
            outfit.range = data.get('range', 300)
            outfit.energy_usage = data.get('energy_usage', 5)
            outfit.accuracy = data.get('accuracy', 1.0)
            outfit.damage_type = data.get('damage_type', DAMAGE_TYPE_ENERGY)
            outfit.projectile_behavior = data.get('projectile_behavior', BEHAVIOR_INSTANT)
            outfit.projectile_speed = data.get('projectile_speed', 10)
            outfit.tracking_strength = data.get('tracking_strength', 0.0)
            outfit.proximity_radius = data.get('proximity_radius', 0)
            outfit.uses_ammo = data.get('uses_ammo', False)
            outfit.ammo_type = data.get('ammo_type', '')
            outfit.max_ammo = data.get('max_ammo', 0)
            outfit.current_ammo = 0
            
            # Visual effects
            if 'beam_color' in data:
                outfit.beam_color = tuple(data['beam_color'])
            
        elif category == "ammunition":
            ammo_type = data.get('ammo_type', 'light_missile')
            outfit = Ammunition(outfit_id, name, ammo_type)
            
            outfit.quantity = data.get('quantity', 10)
            outfit.damage = data.get('damage', 50)
            outfit.projectile_speed = data.get('projectile_speed', 8)
            outfit.projectile_behavior = data.get('projectile_behavior', BEHAVIOR_DUMBFIRE)
            outfit.tracking_strength = data.get('tracking_strength', 0.0)
            outfit.explosion_radius = data.get('explosion_radius', 20)
            outfit.damage_type = data.get('damage_type', DAMAGE_TYPE_EXPLOSIVE)
            
        elif category == "defense":
            outfit = DefenseOutfit(outfit_id, name, subcategory)
            
            outfit.shield_boost = data.get('shield_boost', 0)
            outfit.shield_recharge_boost = data.get('shield_recharge_boost', 0.0)
            outfit.armor_boost = data.get('armor_boost', 0)
            outfit.damage_reduction = data.get('damage_reduction', 0.0)
            outfit.energy_drain = data.get('energy_drain', 0.0)
            
        elif category == "engines":
            outfit = EngineOutfit(outfit_id, name, subcategory)
            
            outfit.acceleration_boost = data.get('acceleration_boost', 0.0)
            outfit.max_speed_boost = data.get('max_speed_boost', 0.0)
            outfit.turn_rate_boost = data.get('turn_rate_boost', 0.0)
            outfit.fuel_efficiency = data.get('fuel_efficiency', 1.0)
            outfit.energy_drain = data.get('energy_drain', 0.0)
            
        elif category == "electronics":
            outfit = ElectronicsOutfit(outfit_id, name, subcategory)
            
            outfit.sensor_range_boost = data.get('sensor_range_boost', 0.0)
            outfit.targeting_boost = data.get('targeting_boost', 0.0)
            outfit.jamming_strength = data.get('jamming_strength', 0.0)
            outfit.communication_range = data.get('communication_range', 0.0)
            outfit.energy_drain = data.get('energy_drain', 0.0)
            outfit.scan_speed_boost = data.get('scan_speed_boost', 0.0)
            
        elif category == "utility":
            outfit = UtilityOutfit(outfit_id, name, subcategory)
            
            outfit.cargo_space_boost = data.get('cargo_space_boost', 0)
            outfit.fuel_capacity_boost = data.get('fuel_capacity_boost', 0)
            outfit.crew_capacity_boost = data.get('crew_capacity_boost', 0)
            outfit.life_support_boost = data.get('life_support_boost', 0.0)
            outfit.energy_generation = data.get('energy_generation', 0.0)
            outfit.energy_storage_boost = data.get('energy_storage_boost', 0.0)
            
        else:
            # Generic outfit
            outfit = StandardizedOutfit(outfit_id, name, category, subcategory)
        
        # Apply common properties
        outfit.space_required = data.get('space_required', 1)
        outfit.mass = data.get('mass', 0.0)
        outfit.cost = data.get('cost', 1000)
        outfit.min_tech_level = data.get('min_tech_level', 1)
        outfit.description = data.get('description', '')
        outfit.outfitter_icon = data.get('outfitter_icon', '')
        
        # Ship size restrictions
        if 'ship_size_restrictions' in data:
            outfit.ship_size_restrictions = data['ship_size_restrictions']
        
        return outfit
        
    except Exception as e:
        print(f"Error creating outfit {outfit_id}: {e}")
        return None

def create_minimal_defaults():
    """Create minimal default outfits if no data files are found."""
    # Basic laser cannon
    laser = Weapon("laser_cannon", "Laser Cannon", "energy")
    laser.cost = 2000
    laser.damage = 15
    laser.fire_rate = 3.0
    laser.range = 400
    laser.energy_usage = 8
    OUTFITS_REGISTRY["laser_cannon"] = laser
    
    # Basic shield
    shield = DefenseOutfit("basic_shield", "Basic Shield Generator", "shields")
    shield.cost = 1500
    shield.shield_boost = 50
    shield.shield_recharge_boost = 1.0
    OUTFITS_REGISTRY["basic_shield"] = shield
    
    # Basic missile launcher
    launcher = Weapon("missile_rack", "Missile Rack", "launcher")
    launcher.cost = 3000
    launcher.uses_ammo = True
    launcher.ammo_type = "light_missile"
    launcher.max_ammo = 20
    launcher.fire_rate = 1.5
    launcher.range = 600
    OUTFITS_REGISTRY["missile_rack"] = launcher
    
    # Basic missile
    missile = Ammunition("light_missile", "Light Missile", "light_missile")
    missile.cost = 150
    missile.quantity = 10
    missile.damage = 40
    missile.projectile_speed = 8
    missile.projectile_behavior = BEHAVIOR_DUMBFIRE
    OUTFITS_REGISTRY["light_missile"] = missile

def save_current_outfits_to_file(file_path="outfits_data.json"):
    """
    Save current outfit registry to a JSON file.
    This can be used to export the current state or create backups.
    """
    try:
        # Make sure we're saving to the game root
        if not os.path.isabs(file_path):
            current_dir = os.path.dirname(__file__)
            game_root = os.path.dirname(os.path.dirname(current_dir))
            file_path = os.path.join(game_root, file_path)
        
        outfits_data = {}
        for outfit_id, outfit in OUTFITS_REGISTRY.items():
            outfit_data = {
                'id': outfit.id,
                'name': outfit.name,
                'category': outfit.category,
                'subcategory': getattr(outfit, 'subcategory', ''),
                'cost': getattr(outfit, 'cost', 1000),
                'space_required': getattr(outfit, 'space_required', 1),
                'min_tech_level': getattr(outfit, 'min_tech_level', 1),
                'outfitter_icon': getattr(outfit, 'outfitter_icon', ''),
                'description': getattr(outfit, 'description', ''),
            }

            # Add category-specific properties
            if outfit.category == "weapons":
                outfit_data.update({
                    'mount_type': getattr(outfit, 'mount_type', 'fixed'),
                    'damage': getattr(outfit, 'damage', 10),
                    'fire_rate': getattr(outfit, 'fire_rate', 1.0),
                    'range': getattr(outfit, 'range', 300),
                    'energy_usage': getattr(outfit, 'energy_usage', 5),
                    'accuracy': getattr(outfit, 'accuracy', 1.0),
                    'damage_type': getattr(outfit, 'damage_type', 'energy'),
                    'projectile_behavior': getattr(outfit, 'projectile_behavior', 'instant'),
                    'projectile_speed': getattr(outfit, 'projectile_speed', 10),
                    'tracking_strength': getattr(outfit, 'tracking_strength', 0.0),
                    'proximity_radius': getattr(outfit, 'proximity_radius', 0),
                    'uses_ammo': getattr(outfit, 'uses_ammo', False),
                    'ammo_type': getattr(outfit, 'ammo_type', ''),
                    'max_ammo': getattr(outfit, 'max_ammo', 0)
                })
                
                # Add beam color if it exists
                if hasattr(outfit, 'beam_color'):
                    outfit_data['beam_color'] = list(outfit.beam_color)
                    
            elif outfit.category == "ammunition":
                outfit_data.update({
                    'ammo_type': getattr(outfit, 'ammo_type', ''),
                    'quantity': getattr(outfit, 'quantity', 10),
                    'damage': getattr(outfit, 'damage', 50),
                    'projectile_speed': getattr(outfit, 'projectile_speed', 8),
                    'projectile_behavior': getattr(outfit, 'projectile_behavior', 'dumbfire'),
                    'tracking_strength': getattr(outfit, 'tracking_strength', 0.0),
                    'explosion_radius': getattr(outfit, 'explosion_radius', 20),
                    'damage_type': getattr(outfit, 'damage_type', 'explosive')
                })
                
            elif outfit.category == "defense":
                outfit_data.update({
                    'shield_boost': getattr(outfit, 'shield_boost', 0),
                    'armor_boost': getattr(outfit, 'armor_boost', 0),
                    'shield_recharge_boost': getattr(outfit, 'shield_recharge_boost', 0.0),
                    'damage_reduction': getattr(outfit, 'damage_reduction', 0.0),
                    'energy_drain': getattr(outfit, 'energy_drain', 0.0)
                })
                
            elif outfit.category == "engines":
                outfit_data.update({
                    'acceleration_boost': getattr(outfit, 'acceleration_boost', 0.0),
                    'max_speed_boost': getattr(outfit, 'max_speed_boost', 0.0),
                    'turn_rate_boost': getattr(outfit, 'turn_rate_boost', 0.0),
                    'fuel_efficiency': getattr(outfit, 'fuel_efficiency', 1.0),
                    'energy_drain': getattr(outfit, 'energy_drain', 0.0)
                })
                
            elif outfit.category == "electronics":
                outfit_data.update({
                    'sensor_range_boost': getattr(outfit, 'sensor_range_boost', 0.0),
                    'targeting_boost': getattr(outfit, 'targeting_boost', 0.0),
                    'jamming_strength': getattr(outfit, 'jamming_strength', 0.0),
                    'communication_range': getattr(outfit, 'communication_range', 0.0),
                    'energy_drain': getattr(outfit, 'energy_drain', 0.0),
                    'scan_speed_boost': getattr(outfit, 'scan_speed_boost', 0.0)
                })
                
            elif outfit.category == "utility":
                outfit_data.update({
                    'cargo_space_boost': getattr(outfit, 'cargo_space_boost', 0),
                    'fuel_capacity_boost': getattr(outfit, 'fuel_capacity_boost', 0),
                    'crew_capacity_boost': getattr(outfit, 'crew_capacity_boost', 0),
                    'life_support_boost': getattr(outfit, 'life_support_boost', 0.0),
                    'energy_generation': getattr(outfit, 'energy_generation', 0.0),
                    'energy_storage_boost': getattr(outfit, 'energy_storage_boost', 0.0)
                })

            # Add ship restrictions if they exist
            if hasattr(outfit, 'ship_size_restrictions') and outfit.ship_size_restrictions:
                outfit_data['ship_size_restrictions'] = outfit.ship_size_restrictions

            outfits_data[outfit_id] = outfit_data

        with open(file_path, 'w') as f:
            json.dump(outfits_data, f, indent=2)

        print(f"Saved {len(outfits_data)} outfits to {file_path}")
        return True

    except Exception as e:
        print(f"Failed to save outfits to {file_path}: {e}")
        return False
