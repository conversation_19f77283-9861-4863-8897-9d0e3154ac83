"""
Utility Editor for the Enhanced Content Editor
Handles editing of utility outfits
"""

import tkinter as tk
from tkinter import ttk, messagebox
from .base_editor import BaseOutfitEditor

class UtilityEditor(BaseOutfitEditor):
    """Editor for utility outfits."""
    
    def __init__(self, parent, data_manager):
        super().__init__(parent, data_manager, "utility")
    
    def setup_editor_ui(self, parent):
        """Setup the utility editor interface."""
        # Basic Properties
        basic_frame = ttk.LabelFrame(parent, text="Basic Properties")
        basic_frame.pack(fill=tk.X, padx=5, pady=5)
        
        row1 = ttk.Frame(basic_frame)
        row1.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(row1, text="Name:").grid(row=0, column=0, sticky=tk.W)
        self.name_var = tk.StringVar()
        ttk.Entry(row1, textvariable=self.name_var, width=25).grid(row=0, column=1, padx=5)
        
        ttk.Label(row1, text="Cost:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.cost_var = tk.IntVar()
        ttk.Spinbox(row1, from_=100, to=100000, textvariable=self.cost_var, width=10).grid(row=0, column=3, padx=5)
        
        row2 = ttk.Frame(basic_frame)
        row2.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(row2, text="Space Required:").grid(row=0, column=0, sticky=tk.W)
        self.space_var = tk.IntVar()
        ttk.Spinbox(row2, from_=1, to=20, textvariable=self.space_var, width=10).grid(row=0, column=1, padx=5)
        
        ttk.Label(row2, text="Type:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.type_var = tk.StringVar()
        ttk.Combobox(row2, textvariable=self.type_var, 
                    values=["cargo", "fuel", "power", "crew", "life_support"], width=15).grid(row=0, column=3, padx=5)
        
        # Image Path
        row3 = ttk.Frame(basic_frame)
        row3.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(row3, text="Image Path:").grid(row=0, column=0, sticky=tk.W)
        self.image_var = tk.StringVar()
        ttk.Entry(row3, textvariable=self.image_var, width=35).grid(row=0, column=1, padx=5)
        ttk.Button(row3, text="Browse", command=lambda: self.browse_image(self.image_var)).grid(row=0, column=2, padx=2)
        
        # Utility Properties
        utility_frame = ttk.LabelFrame(parent, text="Utility Properties")
        utility_frame.pack(fill=tk.X, padx=5, pady=5)
        
        utility_grid = ttk.Frame(utility_frame)
        utility_grid.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(utility_grid, text="Cargo Space Boost:").grid(row=0, column=0, sticky=tk.W)
        self.cargo_var = tk.IntVar()
        ttk.Spinbox(utility_grid, from_=0, to=500, textvariable=self.cargo_var, width=10).grid(row=0, column=1, padx=5)
        
        ttk.Label(utility_grid, text="Fuel Capacity Boost:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.fuel_var = tk.IntVar()
        ttk.Spinbox(utility_grid, from_=0, to=1000, textvariable=self.fuel_var, width=10).grid(row=0, column=3, padx=5)
        
        ttk.Label(utility_grid, text="Energy Generation:").grid(row=1, column=0, sticky=tk.W)
        self.energy_gen_var = tk.DoubleVar()
        ttk.Spinbox(utility_grid, from_=0.0, to=50.0, increment=0.1, textvariable=self.energy_gen_var, width=10).grid(row=1, column=1, padx=5)
        
        ttk.Label(utility_grid, text="Crew Capacity Boost:").grid(row=1, column=2, sticky=tk.W, padx=(10, 0))
        self.crew_var = tk.IntVar()
        ttk.Spinbox(utility_grid, from_=0, to=100, textvariable=self.crew_var, width=10).grid(row=1, column=3, padx=5)
        
        # Save button
        save_frame = ttk.Frame(parent)
        save_frame.pack(fill=tk.X, padx=5, pady=10)
        
        ttk.Button(save_frame, text="Save Utility", command=self.save_item).pack(side=tk.RIGHT, padx=5)
    
    def load_item_into_editor(self, utility):
        """Load utility data into the editor."""
        super().load_item_into_editor(utility)
        
        self.name_var.set(utility.name)
        self.cost_var.set(getattr(utility, 'cost', 1000))
        self.space_var.set(getattr(utility, 'space_required', 1))
        self.type_var.set(getattr(utility, 'subcategory', 'cargo'))
        self.cargo_var.set(getattr(utility, 'cargo_space_boost', 0))
        self.fuel_var.set(getattr(utility, 'fuel_capacity_boost', 0))
        self.energy_gen_var.set(getattr(utility, 'energy_generation', 0.0))
        self.crew_var.set(getattr(utility, 'crew_capacity_boost', 0))
        self.image_var.set(getattr(utility, 'outfitter_icon', ''))
    
    def save_item(self):
        """Save the current utility with editor values."""
        if not self.current_outfit:
            messagebox.showwarning("Warning", "No utility selected to save")
            return
        
        try:
            self.current_outfit.name = self.name_var.get()
            if hasattr(self.current_outfit, 'cost'):
                self.current_outfit.cost = self.cost_var.get()
            if hasattr(self.current_outfit, 'space_required'):
                self.current_outfit.space_required = self.space_var.get()
            if hasattr(self.current_outfit, 'subcategory'):
                self.current_outfit.subcategory = self.type_var.get()
            if hasattr(self.current_outfit, 'cargo_space_boost'):
                self.current_outfit.cargo_space_boost = self.cargo_var.get()
            if hasattr(self.current_outfit, 'fuel_capacity_boost'):
                self.current_outfit.fuel_capacity_boost = self.fuel_var.get()
            if hasattr(self.current_outfit, 'energy_generation'):
                self.current_outfit.energy_generation = self.energy_gen_var.get()
            if hasattr(self.current_outfit, 'crew_capacity_boost'):
                self.current_outfit.crew_capacity_boost = self.crew_var.get()
            if hasattr(self.current_outfit, 'outfitter_icon'):
                self.current_outfit.outfitter_icon = self.image_var.get()
            
            super().save_item()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save utility: {e}")
    
    def create_new_item_instance(self, item_id):
        """Create a new utility instance."""
        class SimpleUtility:
            def __init__(self, id, name):
                self.id = id
                self.name = name
                self.category = "utility"
                self.subcategory = "cargo"
                self.cost = 1000
                self.space_required = 1
                self.cargo_space_boost = 0
                self.fuel_capacity_boost = 0
                self.energy_generation = 0.0
                self.crew_capacity_boost = 0
                self.outfitter_icon = ""
        
        return SimpleUtility(item_id, item_id.replace('_', ' ').title())
