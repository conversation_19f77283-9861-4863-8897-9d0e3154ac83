"""
Defense Editor for the Enhanced Content Editor
Handles editing of defense outfits
"""

import tkinter as tk
from tkinter import ttk, messagebox
from .base_editor import BaseOutfitEditor

class DefenseEditor(BaseOutfitEditor):
    """Editor for defense outfits."""
    
    def __init__(self, parent, data_manager):
        super().__init__(parent, data_manager, "defense")
    
    def setup_editor_ui(self, parent):
        """Setup the defense editor interface."""
        # Create scrollable frame for all defense properties
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Pack scrollbar and canvas
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Basic Properties
        basic_frame = ttk.LabelFrame(scrollable_frame, text="Basic Properties")
        basic_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Name and Cost
        row1 = ttk.Frame(basic_frame)
        row1.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(row1, text="Name:").grid(row=0, column=0, sticky=tk.W)
        self.name_var = tk.StringVar()
        ttk.Entry(row1, textvariable=self.name_var, width=25).grid(row=0, column=1, padx=5)
        
        ttk.Label(row1, text="Cost:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.cost_var = tk.IntVar()
        ttk.Spinbox(row1, from_=100, to=100000, textvariable=self.cost_var, width=10).grid(row=0, column=3, padx=5)
        
        # Space, Type, and Tech Level
        row2 = ttk.Frame(basic_frame)
        row2.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(row2, text="Space Required:").grid(row=0, column=0, sticky=tk.W)
        self.space_var = tk.IntVar()
        ttk.Spinbox(row2, from_=1, to=20, textvariable=self.space_var, width=10).grid(row=0, column=1, padx=5)
        
        ttk.Label(row2, text="Type:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.type_var = tk.StringVar()
        ttk.Combobox(row2, textvariable=self.type_var, 
                    values=["shields", "armor", "point_defense", "ecm", "reactive"], width=15).grid(row=0, column=3, padx=5)
        
        # Tech Level and Image
        row3 = ttk.Frame(basic_frame)
        row3.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(row3, text="Tech Level:").grid(row=0, column=0, sticky=tk.W)
        self.tech_level_var = tk.IntVar()
        ttk.Spinbox(row3, from_=1, to=10, textvariable=self.tech_level_var, width=10).grid(row=0, column=1, padx=5)
        
        ttk.Label(row3, text="Image Path:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.image_var = tk.StringVar()
        ttk.Entry(row3, textvariable=self.image_var, width=25).grid(row=0, column=3, padx=5)
        ttk.Button(row3, text="Browse", command=lambda: self.browse_image(self.image_var)).grid(row=0, column=4, padx=2)
        
        # Passive Defense Properties
        passive_frame = ttk.LabelFrame(scrollable_frame, text="Passive Defense Properties")
        passive_frame.pack(fill=tk.X, padx=5, pady=5)
        
        passive_grid = ttk.Frame(passive_frame)
        passive_grid.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(passive_grid, text="Shield Boost:").grid(row=0, column=0, sticky=tk.W)
        self.shield_boost_var = tk.IntVar()
        ttk.Spinbox(passive_grid, from_=0, to=1000, textvariable=self.shield_boost_var, width=10).grid(row=0, column=1, padx=5)
        
        ttk.Label(passive_grid, text="Armor Boost:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.armor_boost_var = tk.IntVar()
        ttk.Spinbox(passive_grid, from_=0, to=1000, textvariable=self.armor_boost_var, width=10).grid(row=0, column=3, padx=5)
        
        ttk.Label(passive_grid, text="Shield Recharge:").grid(row=1, column=0, sticky=tk.W)
        self.shield_recharge_var = tk.DoubleVar()
        ttk.Spinbox(passive_grid, from_=0.0, to=50.0, increment=0.1, textvariable=self.shield_recharge_var, width=10).grid(row=1, column=1, padx=5)
        
        ttk.Label(passive_grid, text="Damage Reduction:").grid(row=1, column=2, sticky=tk.W, padx=(10, 0))
        self.damage_reduction_var = tk.DoubleVar()
        ttk.Spinbox(passive_grid, from_=0.0, to=0.8, increment=0.01, textvariable=self.damage_reduction_var, width=10).grid(row=1, column=3, padx=5)
        
        # Description
        description_frame = ttk.LabelFrame(scrollable_frame, text="Description")
        description_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(description_frame, text="Description:").pack(anchor=tk.W, padx=5, pady=(5, 0))
        self.description_text = tk.Text(description_frame, height=3, wrap=tk.WORD)
        self.description_text.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        # Save button
        save_frame = ttk.Frame(scrollable_frame)
        save_frame.pack(fill=tk.X, padx=5, pady=10)
        
        ttk.Button(save_frame, text="Save Defense", command=self.save_item).pack(side=tk.RIGHT, padx=5)
    
    def load_item_into_editor(self, defense):
        """Load defense data into the editor."""
        super().load_item_into_editor(defense)
        
        # Load basic properties
        self.name_var.set(defense.name)
        self.cost_var.set(getattr(defense, 'cost', 1000))
        self.space_var.set(getattr(defense, 'space_required', 1))
        self.type_var.set(getattr(defense, 'subcategory', 'shields'))
        self.tech_level_var.set(getattr(defense, 'min_tech_level', 1))
        self.image_var.set(getattr(defense, 'outfitter_icon', ''))
        
        # Load passive defense properties
        self.shield_boost_var.set(getattr(defense, 'shield_boost', 0))
        self.armor_boost_var.set(getattr(defense, 'armor_boost', 0))
        self.shield_recharge_var.set(getattr(defense, 'shield_recharge_boost', 0.0))
        self.damage_reduction_var.set(getattr(defense, 'damage_reduction', 0.0))
        
        # Load description
        self.description_text.delete(1.0, tk.END)
        self.description_text.insert(1.0, getattr(defense, 'description', ''))
    
    def save_item(self):
        """Save the current defense with editor values."""
        if not self.current_outfit:
            messagebox.showwarning("Warning", "No defense selected to save")
            return
        
        try:
            # Update basic properties
            self.current_outfit.name = self.name_var.get()
            if hasattr(self.current_outfit, 'cost'):
                self.current_outfit.cost = self.cost_var.get()
            if hasattr(self.current_outfit, 'space_required'):
                self.current_outfit.space_required = self.space_var.get()
            if hasattr(self.current_outfit, 'subcategory'):
                self.current_outfit.subcategory = self.type_var.get()
            if hasattr(self.current_outfit, 'min_tech_level'):
                self.current_outfit.min_tech_level = self.tech_level_var.get()
            if hasattr(self.current_outfit, 'outfitter_icon'):
                self.current_outfit.outfitter_icon = self.image_var.get()
            
            # Update passive defense properties
            if hasattr(self.current_outfit, 'shield_boost'):
                self.current_outfit.shield_boost = self.shield_boost_var.get()
            if hasattr(self.current_outfit, 'armor_boost'):
                self.current_outfit.armor_boost = self.armor_boost_var.get()
            if hasattr(self.current_outfit, 'shield_recharge_boost'):
                self.current_outfit.shield_recharge_boost = self.shield_recharge_var.get()
            if hasattr(self.current_outfit, 'damage_reduction'):
                self.current_outfit.damage_reduction = self.damage_reduction_var.get()
            
            # Update description
            if hasattr(self.current_outfit, 'description'):
                self.current_outfit.description = self.description_text.get(1.0, tk.END).strip()
            
            super().save_item()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save defense: {e}")
    
    def create_new_item_instance(self, item_id):
        """Create a new defense instance."""
        try:
            # Try to use the standardized system if available
            import sys
            if 'game_objects.standardized_outfits' in sys.modules:
                from game_objects.standardized_outfits import DefenseOutfit
                return DefenseOutfit(item_id, item_id.replace('_', ' ').title(), "shields")
            else:
                # Create a simple object if classes aren't available
                class SimpleDefense:
                    def __init__(self, id, name):
                        self.id = id
                        self.name = name
                        self.category = "defense"
                        self.subcategory = "shields"
                        self.cost = 1000
                        self.space_required = 1
                        self.shield_boost = 0
                        self.armor_boost = 0
                        self.shield_recharge_boost = 0.0
                        self.damage_reduction = 0.0
                        self.description = ""
                
                return SimpleDefense(item_id, item_id.replace('_', ' ').title())
        except ImportError:
            # Fallback to simple object
            class SimpleDefense:
                def __init__(self, id, name):
                    self.id = id
                    self.name = name
                    self.category = "defense"
                    self.subcategory = "shields"
                    self.cost = 1000
                    self.space_required = 1
                    self.shield_boost = 0
                    self.armor_boost = 0
                    self.shield_recharge_boost = 0.0
                    self.damage_reduction = 0.0
                    self.description = ""
            
            return SimpleDefense(item_id, item_id.replace('_', ' ').title())
