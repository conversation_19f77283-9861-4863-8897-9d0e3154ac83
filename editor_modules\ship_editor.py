"""
Ship Editor for the Enhanced Content Editor
Handles editing of ships
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import json

class ShipEditor:
    """Editor for ships."""
    
    def __init__(self, parent, data_manager):
        self.parent = parent
        self.data_manager = data_manager
        self.current_ship = None
        
        # Create main frame
        self.frame = ttk.Frame(parent)
        
        # Setup UI
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the ship editor UI."""
        # Create horizontal layout
        paned = ttk.PanedWindow(self.frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Left panel - Ship list
        left_frame = ttk.LabelFrame(paned, text="Ships Library")
        paned.add(left_frame, weight=1)
        
        # Ship listbox
        self.listbox = tk.Listbox(left_frame)
        self.listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=(5, 0))
        self.listbox.bind("<<ListboxSelect>>", self.on_ship_select)
        
        # Buttons for ship management
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(button_frame, text="New Ship", command=self.create_new_ship).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="Delete", command=self.delete_ship).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="Export", command=self.export_ships).pack(side=tk.LEFT, padx=2)
        
        # Right panel - Editor
        right_frame = ttk.LabelFrame(paned, text="Ship Editor")
        paned.add(right_frame, weight=2)
        
        self.setup_editor_ui(right_frame)
    
    def setup_editor_ui(self, parent):
        """Setup the ship editor interface."""
        # Create scrollable frame
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Basic Properties
        basic_frame = ttk.LabelFrame(scrollable_frame, text="Basic Properties")
        basic_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Name and Cost
        row1 = ttk.Frame(basic_frame)
        row1.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(row1, text="Name:").grid(row=0, column=0, sticky=tk.W)
        self.name_var = tk.StringVar()
        ttk.Entry(row1, textvariable=self.name_var, width=25).grid(row=0, column=1, padx=5)
        
        ttk.Label(row1, text="Cost:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.cost_var = tk.IntVar()
        ttk.Spinbox(row1, from_=1000, to=1000000, textvariable=self.cost_var, width=10).grid(row=0, column=3, padx=5)
        
        # Class and Size
        row2 = ttk.Frame(basic_frame)
        row2.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(row2, text="Ship Class:").grid(row=0, column=0, sticky=tk.W)
        self.ship_class_var = tk.StringVar()
        ttk.Combobox(row2, textvariable=self.ship_class_var, 
                    values=["fighter", "freighter", "transport", "corvette", "frigate", "cruiser", "destroyer", "carrier", "battleship", "dreadnought"], 
                    width=15).grid(row=0, column=1, padx=5)
        
        ttk.Label(row2, text="Size:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.size_var = tk.StringVar()
        ttk.Combobox(row2, textvariable=self.size_var, 
                    values=["small", "medium", "large", "capital"], width=15).grid(row=0, column=3, padx=5)
        
        # Images
        row3 = ttk.Frame(basic_frame)
        row3.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(row3, text="Shipyard Image:").grid(row=0, column=0, sticky=tk.W)
        self.shipyard_image_var = tk.StringVar()
        ttk.Entry(row3, textvariable=self.shipyard_image_var, width=25).grid(row=0, column=1, padx=5)
        ttk.Button(row3, text="Browse", command=lambda: self.browse_image(self.shipyard_image_var)).grid(row=0, column=2, padx=2)
        
        ttk.Label(row3, text="Sprite:").grid(row=0, column=3, sticky=tk.W, padx=(10, 0))
        self.sprite_var = tk.StringVar()
        ttk.Entry(row3, textvariable=self.sprite_var, width=25).grid(row=0, column=4, padx=5)
        ttk.Button(row3, text="Browse", command=lambda: self.browse_image(self.sprite_var)).grid(row=0, column=5, padx=2)
        
        # Performance Stats
        performance_frame = ttk.LabelFrame(scrollable_frame, text="Performance Stats")
        performance_frame.pack(fill=tk.X, padx=5, pady=5)
        
        perf_grid = ttk.Frame(performance_frame)
        perf_grid.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(perf_grid, text="Max Speed:").grid(row=0, column=0, sticky=tk.W)
        self.max_speed_var = tk.DoubleVar()
        ttk.Spinbox(perf_grid, from_=0.1, to=20.0, increment=0.1, textvariable=self.max_speed_var, width=10).grid(row=0, column=1, padx=5)
        
        ttk.Label(perf_grid, text="Acceleration:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.acceleration_var = tk.DoubleVar()
        ttk.Spinbox(perf_grid, from_=0.1, to=10.0, increment=0.1, textvariable=self.acceleration_var, width=10).grid(row=0, column=3, padx=5)
        
        ttk.Label(perf_grid, text="Turn Rate:").grid(row=1, column=0, sticky=tk.W)
        self.turn_rate_var = tk.DoubleVar()
        ttk.Spinbox(perf_grid, from_=0.1, to=5.0, increment=0.1, textvariable=self.turn_rate_var, width=10).grid(row=1, column=1, padx=5)
        
        # Capacity Stats
        capacity_frame = ttk.LabelFrame(scrollable_frame, text="Capacity Stats")
        capacity_frame.pack(fill=tk.X, padx=5, pady=5)
        
        cap_grid = ttk.Frame(capacity_frame)
        cap_grid.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(cap_grid, text="Cargo Space:").grid(row=0, column=0, sticky=tk.W)
        self.cargo_space_var = tk.IntVar()
        ttk.Spinbox(cap_grid, from_=0, to=1000, textvariable=self.cargo_space_var, width=10).grid(row=0, column=1, padx=5)
        
        ttk.Label(cap_grid, text="Outfit Space:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.outfit_space_var = tk.IntVar()
        ttk.Spinbox(cap_grid, from_=1, to=500, textvariable=self.outfit_space_var, width=10).grid(row=0, column=3, padx=5)
        
        # Defense Stats
        defense_frame = ttk.LabelFrame(scrollable_frame, text="Defense Stats")
        defense_frame.pack(fill=tk.X, padx=5, pady=5)
        
        def_grid = ttk.Frame(defense_frame)
        def_grid.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(def_grid, text="Shields:").grid(row=0, column=0, sticky=tk.W)
        self.shields_var = tk.IntVar()
        ttk.Spinbox(def_grid, from_=0, to=2000, textvariable=self.shields_var, width=10).grid(row=0, column=1, padx=5)
        
        ttk.Label(def_grid, text="Armor:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.armor_var = tk.IntVar()
        ttk.Spinbox(def_grid, from_=0, to=2000, textvariable=self.armor_var, width=10).grid(row=0, column=3, padx=5)
        
        ttk.Label(def_grid, text="Shield Recharge:").grid(row=1, column=0, sticky=tk.W)
        self.shield_recharge_var = tk.DoubleVar()
        ttk.Spinbox(def_grid, from_=0.0, to=50.0, increment=0.1, textvariable=self.shield_recharge_var, width=10).grid(row=1, column=1, padx=5)
        
        # Description
        description_frame = ttk.LabelFrame(scrollable_frame, text="Description")
        description_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(description_frame, text="Description:").pack(anchor=tk.W, padx=5, pady=(5, 0))
        self.description_text = tk.Text(description_frame, height=4, wrap=tk.WORD)
        self.description_text.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        # Save button
        save_frame = ttk.Frame(scrollable_frame)
        save_frame.pack(fill=tk.X, padx=5, pady=10)
        
        ttk.Button(save_frame, text="Save Ship", command=self.save_ship).pack(side=tk.RIGHT, padx=5)
    
    def browse_image(self, var):
        """Browse for image file."""
        from tkinter import filedialog
        filename = filedialog.askopenfilename(
            title="Select Image",
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"), ("All files", "*.*")]
        )
        if filename:
            var.set(filename)
    
    def load_data(self):
        """Load ships into the listbox."""
        if hasattr(self, 'listbox'):
            self.listbox.delete(0, tk.END)
            
            ships = self.data_manager.get_all_ships()
            for ship_id, ship in ships.items():
                ship_class = getattr(ship, 'ship_class', 'unknown')
                size = getattr(ship, 'size', 'unknown')
                display_name = f"{ship.name} ({ship_class}, {size})"
                self.listbox.insert(tk.END, display_name)
    
    def on_ship_select(self, event=None):
        """Handle ship selection from list."""
        selection = self.listbox.curselection()
        if not selection:
            return
        
        # Get selected ship
        ships = list(self.data_manager.get_all_ships().values())
        
        if selection[0] < len(ships):
            ship = ships[selection[0]]
            self.load_ship_into_editor(ship)
    
    def load_ship_into_editor(self, ship):
        """Load ship data into the editor."""
        self.current_ship = ship
        
        # Load basic properties
        self.name_var.set(ship.name)
        self.cost_var.set(getattr(ship, 'cost', 10000))
        self.ship_class_var.set(getattr(ship, 'ship_class', 'fighter'))
        self.size_var.set(getattr(ship, 'size', 'small'))
        
        # Load performance stats
        self.max_speed_var.set(getattr(ship, 'max_speed', 3.0))
        self.acceleration_var.set(getattr(ship, 'acceleration', 0.5))
        self.turn_rate_var.set(getattr(ship, 'turn_rate', 1.0))
        
        # Load capacity stats
        self.cargo_space_var.set(getattr(ship, 'cargo_space', 10))
        self.outfit_space_var.set(getattr(ship, 'outfit_space', 20))
        
        # Load defense stats
        self.shields_var.set(getattr(ship, 'shields', 50))
        self.armor_var.set(getattr(ship, 'armor', 30))
        self.shield_recharge_var.set(getattr(ship, 'shield_recharge_rate', 1.0))
        
        # Load description
        self.description_text.delete(1.0, tk.END)
        self.description_text.insert(1.0, getattr(ship, 'description', ''))
        
        # Load images
        self.shipyard_image_var.set(getattr(ship, 'shipyard_image', ''))
        self.sprite_var.set(getattr(ship, 'sprite', ''))
    
    def save_ship(self):
        """Save the current ship with editor values."""
        if not self.current_ship:
            messagebox.showwarning("Warning", "No ship selected to save")
            return
        
        try:
            # Update basic properties
            self.current_ship.name = self.name_var.get()
            if hasattr(self.current_ship, 'cost'):
                self.current_ship.cost = self.cost_var.get()
            if hasattr(self.current_ship, 'ship_class'):
                self.current_ship.ship_class = self.ship_class_var.get()
            if hasattr(self.current_ship, 'size'):
                self.current_ship.size = self.size_var.get()
            
            # Update performance stats
            if hasattr(self.current_ship, 'max_speed'):
                self.current_ship.max_speed = self.max_speed_var.get()
            if hasattr(self.current_ship, 'acceleration'):
                self.current_ship.acceleration = self.acceleration_var.get()
            if hasattr(self.current_ship, 'turn_rate'):
                self.current_ship.turn_rate = self.turn_rate_var.get()
            
            # Update capacity stats
            if hasattr(self.current_ship, 'cargo_space'):
                self.current_ship.cargo_space = self.cargo_space_var.get()
            if hasattr(self.current_ship, 'outfit_space'):
                self.current_ship.outfit_space = self.outfit_space_var.get()
            
            # Update defense stats
            if hasattr(self.current_ship, 'shields'):
                self.current_ship.shields = self.shields_var.get()
                self.current_ship.max_shields = self.shields_var.get()
            if hasattr(self.current_ship, 'armor'):
                self.current_ship.armor = self.armor_var.get()
                self.current_ship.max_armor = self.armor_var.get()
            if hasattr(self.current_ship, 'shield_recharge_rate'):
                self.current_ship.shield_recharge_rate = self.shield_recharge_var.get()
            
            # Update description
            if hasattr(self.current_ship, 'description'):
                self.current_ship.description = self.description_text.get(1.0, tk.END).strip()
            
            # Update images
            if hasattr(self.current_ship, 'shipyard_image'):
                self.current_ship.shipyard_image = self.shipyard_image_var.get()
            if hasattr(self.current_ship, 'sprite'):
                self.current_ship.sprite = self.sprite_var.get()
            
            messagebox.showinfo("Success", f"Saved ship: {self.current_ship.name}")
            self.load_data()  # Refresh the list
            self.data_manager.auto_save()  # Auto-save changes
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save ship: {e}")
    
    def create_new_ship(self):
        """Create a new ship."""
        ship_id = simpledialog.askstring("New Ship", "Enter a unique ID for the new ship:")
        if not ship_id:
            return
        
        ships = self.data_manager.get_all_ships()
        if ship_id in ships:
            messagebox.showerror("Error", f"A ship with ID '{ship_id}' already exists")
            return
        
        try:
            # Create a simple ship object
            class SimpleShip:
                def __init__(self, id, name):
                    self.id = id
                    self.name = name
                    self.ship_class = "fighter"
                    self.size = "small"
                    self.cost = 10000
                    self.outfit_space = 20
                    self.cargo_space = 10
                    self.max_speed = 3.0
                    self.acceleration = 0.5
                    self.turn_rate = 1.0
                    self.shields = 50
                    self.armor = 30
                    self.shield_recharge_rate = 1.0
                    self.description = ""
                    self.shipyard_image = ""
                    self.sprite = ""
            
            new_ship = SimpleShip(ship_id, ship_id.replace('_', ' ').title())
            
            # Add to ships registry
            self.data_manager.ships_registry[ship_id] = new_ship
            
            self.load_data()
            messagebox.showinfo("Success", f"Created new ship: {new_ship.name}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to create ship: {e}")
    
    def delete_ship(self):
        """Delete the selected ship."""
        selection = self.listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "No ship selected to delete")
            return
        
        # Get selected ship
        ships = list(self.data_manager.get_all_ships().items())
        
        if selection[0] < len(ships):
            ship_id, ship = ships[selection[0]]
            
            result = messagebox.askyesno("Confirm Delete",
                                       f"Are you sure you want to delete '{ship.name}'?")
            if result:
                del self.data_manager.ships_registry[ship_id]
                self.load_data()
                messagebox.showinfo("Success", f"Deleted ship: {ship.name}")
    
    def export_ships(self):
        """Export ships to JSON file."""
        filename = filedialog.asksaveasfilename(
            title="Export Ships",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            try:
                ships_data = {}
                ships = self.data_manager.get_all_ships()
                
                for ship_id, ship in ships.items():
                    ships_data[ship_id] = self.data_manager._ship_to_dict(ship)
                
                with open(filename, 'w') as f:
                    json.dump(ships_data, f, indent=2)
                
                messagebox.showinfo("Success", f"Exported {len(ships_data)} ships to {filename}")
                
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export ships: {e}")
