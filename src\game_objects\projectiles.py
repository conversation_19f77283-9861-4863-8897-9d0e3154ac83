"""
Projectile definitions for Escape Velocity Py.
This module contains all projectile types that can be fired by weapons.
"""

import pygame as pg
import math
from game_objects.outfits import WEAPON_TYPE_LASER, WEAPON_TYPE_MISSILE, WEAPON_TYPE_PROJECTILE, WEAPON_TYPE_BEAM

# Projectile types
PROJECTILE_TYPE_LASER = "laser"
PROJECTILE_TYPE_MISSILE = "missile"
PROJECTILE_TYPE_PROJECTILE = "projectile"
PROJECTILE_TYPE_BEAM = "beam"

# Projectile colors
LASER_COLOR = (255, 0, 0)  # Red
MISSILE_COLOR = (255, 128, 0)  # Orange
PROJECTILE_COLOR = (0, 255, 255)  # Cyan
BEAM_COLOR = (255, 0, 255)  # Magenta

class Projectile(pg.sprite.Sprite):
    """Base class for all projectiles."""

    def __init__(self, game, owner, pos, angle, speed, damage, range, color=(255, 255, 255),
                 size=(4, 4), lifetime=None, image_path=None):
        """
        Initialize a projectile.

        Args:
            game: The game instance
            owner: The entity that fired the projectile
            pos (Vector2): Starting position
            angle (float): Direction angle in degrees
            speed (float): Projectile speed
            damage (float): Damage on hit
            range (float): Maximum travel distance
            color (tuple): RGB color for default rendering
            size (tuple): Width and height for default rendering
            lifetime (float): Maximum lifetime in seconds (if None, calculated from range/speed)
            image_path (str): Path to projectile image (optional)
        """
        super().__init__()
        self.game = game
        self.owner = owner
        self.pos = pg.math.Vector2(pos)
        self.angle = angle
        self.speed = speed
        self.damage = damage
        self.range = range
        self.color = color
        self.size = size

        # Calculate direction vector from angle (0 degrees is North/Up)
        # Convert to math angle (0 degrees is East/Right) for cos/sin, then adjust for Pygame's Y-down
        math_angle_rad = math.radians(self.angle - 90) # e.g. North (0) becomes -90 deg, East (90) becomes 0 deg
        self.direction = pg.math.Vector2(
            math.cos(math_angle_rad),
            math.sin(math_angle_rad) # For Pygame: positive sin() is downwards, negative sin() is upwards.
                                     # A ship angle of 0 (North) means math_angle -90, sin(-90) = -1 (upwards)
                                     # A ship angle of 90 (East) means math_angle 0, cos(0) = 1 (rightwards)
        ).normalize()

        # Calculate lifetime based on range and speed if not provided
        self.lifetime = lifetime if lifetime is not None else range / speed
        self.time_alive = 0
        self.distance_traveled = 0
        self.start_pos = pg.math.Vector2(pos)

        # Load image or create default
        if image_path:
            try:
                self.image = pg.image.load(image_path).convert_alpha()
            except:
                self.create_default_image()
        else:
            self.create_default_image()

        self.rect = self.image.get_rect()
        self.rect.center = self.pos

    def create_default_image(self):
        """Create a default image for the projectile."""
        self.image = pg.Surface(self.size, pg.SRCALPHA)
        pg.draw.rect(self.image, self.color, (0, 0, self.size[0], self.size[1]))

    def update(self, dt):
        """
        Update projectile position and check for collisions.

        Args:
            dt (float): Time delta in seconds

        Returns:
            bool: True if the projectile should be removed
        """
        # Update lifetime and check if expired
        self.time_alive += dt
        if self.time_alive >= self.lifetime:
            return True  # Remove projectile

        # Move projectile
        movement = self.direction * self.speed * dt
        self.pos += movement
        self.distance_traveled += movement.length()

        # Check if out of range
        if self.distance_traveled >= self.range:
            return True  # Remove projectile

        # Update rect position
        self.rect.center = self.pos

        # Check for collisions (to be implemented by subclasses)
        if self.check_collisions():
            return True  # Remove projectile

        return False

    def check_collisions(self):
        """
        Check for collisions with other entities.

        Returns:
            bool: True if collision occurred
        """
        # Base implementation - override in subclasses
        return False

    def on_hit(self, target):
        """
        Handle collision with a target.

        Args:
            target: The entity that was hit
        """
        # Apply damage if target has a take_damage method
        if hasattr(target, 'take_damage'):
            target.take_damage(self.damage, self.owner.faction_id if hasattr(self.owner, 'faction_id') else None)


class LaserProjectile(Projectile):
    """Laser projectile - fast and straight."""

    def __init__(self, game, owner, pos, angle, damage, range, color=LASER_COLOR, beam=False):
        """
        Initialize a laser projectile.

        Args:
            game: The game instance
            owner: The entity that fired the laser
            pos (Vector2): Starting position
            angle (float): Direction angle in degrees
            damage (float): Damage on hit
            range (float): Maximum travel distance
            color (tuple): RGB color for the laser
            beam (bool): Whether this is a continuous beam weapon
        """
        # Lasers are fast
        speed = 800

        # Beam weapons are longer
        if beam:
            size = (range, 2)  # Very long beam
            color = (color[0], color[1], color[2], 180)  # Semi-transparent
        else:
            size = (8, 2)  # Standard laser bolt

        # Rotate size based on angle
        if 45 < angle % 180 < 135:
            size = (size[1], size[0])  # Swap dimensions for vertical orientation

        super().__init__(game, owner, pos, angle, speed, damage, range, color, size)

        self.beam = beam

        # For beam weapons, reduce lifetime
        if beam:
            self.lifetime = 0.1  # Short lifetime for beam weapons

        # Rotate the image to match the angle
        self.image = pg.transform.rotate(self.image, angle)
        self.rect = self.image.get_rect(center=self.pos)

    def create_default_image(self):
        """Create a laser beam image."""
        self.image = pg.Surface(self.size, pg.SRCALPHA)

        if hasattr(self, 'beam') and self.beam:
            # For beam weapons, create a gradient effect
            for i in range(self.size[0]):
                # Fade out towards the end
                alpha = max(0, 255 - (i / self.size[0]) * 200)
                color = (self.color[0], self.color[1], self.color[2], int(alpha))
                pg.draw.line(self.image, color, (i, 0), (i, self.size[1]), 1)
        else:
            # Standard laser bolt
            pg.draw.rect(self.image, self.color, (0, 0, self.size[0], self.size[1]))

    def check_collisions(self):
        """Check for collisions with ships and planets."""
        # Don't collide with the owner
        for ship in self.game.ai_ships:
            if ship != self.owner and ship.rect.colliderect(self.rect):
                self.on_hit(ship)
                return True

        # Check collision with player if not fired by player
        if self.owner != self.game.player and self.game.player.rect.colliderect(self.rect):
            self.on_hit(self.game.player)
            return True

        # Check collision with planets
        for planet in self.game.planets:
            if planet.rect.colliderect(self.rect):
                # Optional: could add visual effect for hitting a planet
                return True

        return False


class MissileProjectile(Projectile):
    """Guided missile that homes in on a target."""

    def __init__(self, game, owner, pos, angle, damage, range, target=None,
                 tracking=0.8, color=MISSILE_COLOR):
        """
        Initialize a missile projectile.

        Args:
            game: The game instance
            owner: The entity that fired the missile
            pos (Vector2): Starting position
            angle (float): Initial direction angle in degrees
            damage (float): Damage on hit
            range (float): Maximum travel distance
            target: The target to track (optional)
            tracking (float): How well the missile tracks its target (0.0-1.0)
            color (tuple): RGB color for default rendering
        """
        # Missiles are slower but have longer range
        speed = 300
        size = (6, 6)

        super().__init__(game, owner, pos, angle, speed, damage, range, color, size)

        self.target = target
        self.tracking = tracking  # How well the missile tracks (0.0-1.0)
        self.turn_rate = 120 * tracking  # Degrees per second, adjusted by tracking
        self.acceleration = 100 * tracking  # Acceleration rate, adjusted by tracking
        self.max_speed = 500  # Maximum speed
        self.current_speed = speed * 0.5  # Start slower
        self.guided = True

        # Store original image for rotation
        self.original_image = self.image.copy()

    def create_default_image(self):
        """Create a missile image."""
        self.image = pg.Surface((8, 8), pg.SRCALPHA)
        # Draw a small triangle for the missile
        pg.draw.polygon(self.image, self.color, [(4, 0), (0, 8), (8, 8)])

    def update(self, dt):
        """Update missile position and tracking."""
        # If we have a target and it's still alive, track it
        if self.target and hasattr(self.target, 'alive') and self.target.alive():
            # Calculate direction to target
            to_target = self.target.pos - self.pos
            if to_target.length_squared() > 0:
                to_target = to_target.normalize()

                # Calculate angle to target
                target_angle = math.degrees(math.atan2(-to_target.y, to_target.x))

                # Calculate angle difference (shortest path)
                angle_diff = (target_angle - self.angle) % 360
                if angle_diff > 180:
                    angle_diff -= 360

                # Turn towards target with limited turn rate
                turn_amount = min(abs(angle_diff), self.turn_rate * dt)
                if angle_diff < 0:
                    self.angle -= turn_amount
                else:
                    self.angle += turn_amount

                # Recalculate direction vector
                self.direction = pg.math.Vector2(
                    math.cos(math.radians(self.angle)),
                    -math.sin(math.radians(self.angle))
                ).normalize()

                # Accelerate
                self.current_speed = min(self.current_speed + self.acceleration * dt, self.max_speed)

                # Rotate image to match direction
                self.image = pg.transform.rotate(self.original_image, self.angle)
                self.rect = self.image.get_rect(center=self.pos)

        # Use current speed instead of constant speed
        movement = self.direction * self.current_speed * dt
        self.pos += movement
        self.distance_traveled += movement.length()

        # Update lifetime and check if expired
        self.time_alive += dt
        if self.time_alive >= self.lifetime or self.distance_traveled >= self.range:
            return True  # Remove projectile

        # Update rect position
        self.rect.center = self.pos

        # Check for collisions
        if self.check_collisions():
            return True  # Remove projectile

        return False

    def check_collisions(self):
        """Check for collisions with ships and planets."""
        # Don't collide with the owner
        for ship in self.game.ai_ships:
            if ship != self.owner and ship.rect.colliderect(self.rect):
                self.on_hit(ship)
                return True

        # Check collision with player if not fired by player
        if self.owner != self.game.player and self.game.player.rect.colliderect(self.rect):
            self.on_hit(self.game.player)
            return True

        # Check collision with planets
        for planet in self.game.planets:
            if planet.rect.colliderect(self.rect):
                # Optional: could add explosion effect
                return True

        return False
