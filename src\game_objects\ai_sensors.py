"""
AI Sensors Module - Target detection and threat assessment
This module handles AI ship sensors, target detection, and threat evaluation.
"""
import pygame as pg
import random
import math
from game_objects.ai_core import *

class AISensorManager:
    """
    Manages AI ship sensors and target detection.
    Handles threat assessment and target prioritization.
    """

    def __init__(self, ai_ship):
        self.ship = ai_ship
        self.scan_timer = 0
        self.scan_interval = 30  # Scan every 0.5 seconds at 60 FPS
        self.detected_targets = []
        self.threat_levels = {}

    def update(self, dt):
        """Update sensor systems."""
        self.scan_timer -= 1
        if self.scan_timer <= 0:
            self.scan_for_targets()
            self.scan_timer = self.scan_interval

        # Update threat assessments
        self._update_threat_assessments()

    def scan_for_targets(self):
        """Scan for potential targets within sensor range."""
        self.detected_targets.clear()

        # Check player
        player = self.ship.game.player
        if player and player.alive():
            dist_to_player = self.ship.pos.distance_to(player.pos)
            if dist_to_player < AI_SENSOR_RANGE:
                relation_to_player = self.ship.game.faction_relations.get(
                    self.ship.faction_id, {}
                ).get(self.ship.game.player_faction_id, 0.0)

                if relation_to_player < -0.5:
                    self.detected_targets.append({
                        'entity': player,
                        'distance': dist_to_player,
                        'relation': relation_to_player,
                        'type': 'player'
                    })

        # Check other AI ships
        for ship in self.ship.game.ai_ships:
            if ship == self.ship or not ship.alive():
                continue

            dist_to_ship = self.ship.pos.distance_to(ship.pos)
            if dist_to_ship < AI_SENSOR_RANGE:
                relation_to_other_ai = self.ship.game.faction_relations.get(
                    self.ship.faction_id, {}
                ).get(ship.faction_id, 0.0)

                if relation_to_other_ai < -0.5:
                    self.detected_targets.append({
                        'entity': ship,
                        'distance': dist_to_ship,
                        'relation': relation_to_other_ai,
                        'type': 'ai_ship'
                    })

        # Select best target if we found any
        if self.detected_targets and not self.ship.target_entity:
            best_target = self._select_best_target()
            if best_target:
                self.ship.target_entity = best_target['entity']
                self.ship.ai_state = AI_STATE_ATTACKING
                print(f"{self.ship.faction_id} ship attacking {best_target['type']}!")

    def _select_best_target(self):
        """
        Select the best target from detected targets.

        Returns:
            dict: Best target info, or None if no suitable target
        """
        if not self.detected_targets:
            return None

        # Score each target
        best_target = None
        best_score = -1

        for target_info in self.detected_targets:
            score = self._calculate_target_priority(target_info)
            if score > best_score:
                best_score = score
                best_target = target_info

        return best_target

    def _calculate_target_priority(self, target_info):
        """
        Calculate priority score for a target.

        Args:
            target_info: Dictionary with target information

        Returns:
            float: Priority score (higher = higher priority)
        """
        entity = target_info['entity']
        distance = target_info['distance']
        relation = target_info['relation']
        target_type = target_info['type']

        # Base score from hostility level
        base_score = abs(relation) * 100

        # Distance factor (closer targets are higher priority)
        distance_factor = max(0.1, 1.0 - (distance / AI_SENSOR_RANGE))

        # Target type modifiers
        type_modifier = 1.0
        if target_type == 'player':
            type_modifier = 1.5  # Player is higher priority
        elif target_type == 'ai_ship':
            # Consider ship size and threat level
            if hasattr(entity, 'ship') and entity.ship:
                ship_size_modifier = {
                    'small': 0.8,
                    'medium': 1.0,
                    'large': 1.2,
                    'capital': 1.5
                }.get(entity.ship.size, 1.0)
                type_modifier *= ship_size_modifier

        # Threat assessment
        threat_modifier = self._assess_threat_level(entity)

        # Personality modifiers
        personality_modifier = 1.0
        if self.ship.personality == AI_PERSONALITY_AGGRESSIVE:
            personality_modifier = 1.3
        elif self.ship.personality == AI_PERSONALITY_DEFENSIVE:
            personality_modifier = 0.8
        elif self.ship.personality == AI_PERSONALITY_COWARD:
            personality_modifier = 0.5

        total_score = (base_score * distance_factor * type_modifier *
                      threat_modifier * personality_modifier)

        return total_score

    def _assess_threat_level(self, entity):
        """
        Assess the threat level of an entity.

        Args:
            entity: The entity to assess

        Returns:
            float: Threat modifier (higher = more threatening)
        """
        if not hasattr(entity, 'weapons') or not entity.weapons:
            return 0.5  # Low threat if no weapons

        # Calculate threat based on weapons and ship stats
        weapon_threat = 0
        for weapon in entity.weapons:
            weapon_dps = weapon.damage * weapon.fire_rate
            weapon_threat += weapon_dps

        # Normalize threat level
        threat_level = min(2.0, weapon_threat / 50.0)  # Cap at 2x modifier

        # Consider ship health/shields
        if hasattr(entity, 'health') and hasattr(entity, 'max_health'):
            health_ratio = entity.health / entity.max_health
            threat_level *= health_ratio

        return max(0.1, threat_level)

    def _update_threat_assessments(self):
        """Update threat assessments for known entities."""
        # Clean up old threat assessments
        current_entities = set()

        # Add current target
        if self.ship.target_entity:
            current_entities.add(id(self.ship.target_entity))

        # Add detected targets
        for target_info in self.detected_targets:
            current_entities.add(id(target_info['entity']))

        # Remove old entries
        old_entities = set(self.threat_levels.keys()) - current_entities
        for entity_id in old_entities:
            del self.threat_levels[entity_id]

        # Update current threat levels
        for target_info in self.detected_targets:
            entity_id = id(target_info['entity'])
            self.threat_levels[entity_id] = self._assess_threat_level(target_info['entity'])

    def get_nearest_enemy(self):
        """
        Get the nearest enemy within sensor range.

        Returns:
            Entity or None if no enemies detected
        """
        if not self.detected_targets:
            return None

        nearest_target = min(self.detected_targets, key=lambda t: t['distance'])
        return nearest_target['entity']

    def get_most_threatening_enemy(self):
        """
        Get the most threatening enemy within sensor range.

        Returns:
            Entity or None if no enemies detected
        """
        if not self.detected_targets:
            return None

        most_threatening = max(self.detected_targets,
                             key=lambda t: self._assess_threat_level(t['entity']))
        return most_threatening['entity']

    def is_under_attack(self):
        """
        Determine if the ship is currently under attack.

        Returns:
            bool: True if under attack
        """
        # Check if any nearby enemies are targeting us
        for target_info in self.detected_targets:
            entity = target_info['entity']
            if (hasattr(entity, 'target_entity') and
                entity.target_entity == self.ship and
                target_info['distance'] < 300):
                return True

        return False

    def should_flee(self):
        """
        Determine if the ship should flee from combat.

        Returns:
            bool: True if should flee
        """
        # Health-based flee decision
        health_ratio = self.ship.health / self.ship.max_health

        # Personality affects flee threshold
        flee_threshold = 0.3  # Default 30% health
        if self.ship.personality == AI_PERSONALITY_AGGRESSIVE:
            flee_threshold = 0.15  # Fight to 15% health
        elif self.ship.personality == AI_PERSONALITY_DEFENSIVE:
            flee_threshold = 0.5   # Flee at 50% health
        elif self.ship.personality == AI_PERSONALITY_COWARD:
            flee_threshold = 0.7   # Flee at 70% health

        # Add some randomness
        personality_factor = random.uniform(0.8, 1.2)
        flee_threshold *= personality_factor

        if health_ratio < flee_threshold:
            return True

        # Also consider being outnumbered
        if len(self.detected_targets) >= 3:
            return True

        return False
