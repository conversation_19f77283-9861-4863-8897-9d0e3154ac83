"""
Escape Velocity Py - Game Content Editor - Basic Version
Simple editor for weapons and outfits to get started
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
import os
import sys
from pathlib import Path

# Add the game's src directory to the path
game_src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(game_src_path))

try:
    from game_objects.standardized_outfits import *
    from game_objects.example_outfits import *
    print("Successfully imported outfit system")
except ImportError as e:
    print(f"Error importing outfit system: {e}")
    print("Make sure the game files are in the correct location")

class SimpleEditor:
    def __init__(self, root):
        self.root = root
        self.root.title("EV Py - Simple Content Editor")
        self.root.geometry("1000x700")
        
        self.current_outfit = None
        self.setup_ui()
        self.load_outfits()
        
    def setup_ui(self):
        """Setup basic UI with outfit list and editor."""
        # Main layout
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Left side - Outfit list
        left_frame = ttk.LabelFrame(main_frame, text="Outfits")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=False, padx=(0, 5))
        
        # Outfit listbox
        self.outfit_listbox = tk.Listbox(left_frame, width=30)
        self.outfit_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.outfit_listbox.bind("<<ListboxSelect>>", self.on_outfit_select)
        
        # Buttons
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(button_frame, text="New Weapon", command=self.new_weapon).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="New Defense", command=self.new_defense).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="Delete", command=self.delete_outfit).pack(side=tk.LEFT, padx=2)
        
        # Right side - Editor
        self.editor_frame = ttk.LabelFrame(main_frame, text="Outfit Editor")
        self.editor_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        self.setup_outfit_editor()
        
    def setup_outfit_editor(self):
        """Setup the outfit editor interface."""
        # Basic properties
        basic_frame = ttk.LabelFrame(self.editor_frame, text="Basic Properties")
        basic_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Row 1
        row1 = ttk.Frame(basic_frame)
        row1.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(row1, text="ID:").grid(row=0, column=0, sticky=tk.W)
        self.id_var = tk.StringVar()
        ttk.Entry(row1, textvariable=self.id_var, width=20).grid(row=0, column=1, padx=5)
        
        ttk.Label(row1, text="Name:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.name_var = tk.StringVar()
        ttk.Entry(row1, textvariable=self.name_var, width=25).grid(row=0, column=3, padx=5)
        
        # Row 2
        row2 = ttk.Frame(basic_frame)
        row2.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(row2, text="Category:").grid(row=0, column=0, sticky=tk.W)
        self.category_var = tk.StringVar()
        category_combo = ttk.Combobox(row2, textvariable=self.category_var, 
                                     values=["weapons", "defense", "engines", "utility"], width=15)
        category_combo.grid(row=0, column=1, padx=5)
        category_combo.bind("<<ComboboxSelected>>", self.on_category_change)
        
        ttk.Label(row2, text="Cost:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.cost_var = tk.IntVar()
        ttk.Spinbox(row2, from_=0, to=1000000, textvariable=self.cost_var, width=15).grid(row=0, column=3, padx=5)
        
        # Row 3
        row3 = ttk.Frame(basic_frame)
        row3.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(row3, text="Space:").grid(row=0, column=0, sticky=tk.W)
        self.space_var = tk.IntVar()
        ttk.Spinbox(row3, from_=0, to=50, textvariable=self.space_var, width=10).grid(row=0, column=1, padx=5)
        
        ttk.Label(row3, text="Tech Level:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.tech_var = tk.IntVar()
        ttk.Spinbox(row3, from_=1, to=5, textvariable=self.tech_var, width=5).grid(row=0, column=3, padx=5)
        
        # Description
        desc_frame = ttk.Frame(basic_frame)
        desc_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(desc_frame, text="Description:").pack(anchor=tk.W)
        self.description_text = tk.Text(desc_frame, height=3)
        self.description_text.pack(fill=tk.X, pady=2)
        
        # Weapon-specific properties
        self.weapon_frame = ttk.LabelFrame(self.editor_frame, text="Weapon Properties")
        
        weapon_grid = ttk.Frame(self.weapon_frame)
        weapon_grid.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(weapon_grid, text="Damage:").grid(row=0, column=0, sticky=tk.W)
        self.damage_var = tk.IntVar()
        ttk.Spinbox(weapon_grid, from_=1, to=1000, textvariable=self.damage_var, width=10).grid(row=0, column=1, padx=5)
        
        ttk.Label(weapon_grid, text="Fire Rate:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.fire_rate_var = tk.DoubleVar()
        ttk.Spinbox(weapon_grid, from_=0.1, to=10.0, increment=0.1, textvariable=self.fire_rate_var, width=10).grid(row=0, column=3, padx=5)
        
        ttk.Label(weapon_grid, text="Range:").grid(row=1, column=0, sticky=tk.W)
        self.range_var = tk.IntVar()
        ttk.Spinbox(weapon_grid, from_=50, to=2000, textvariable=self.range_var, width=10).grid(row=1, column=1, padx=5)
        
        ttk.Label(weapon_grid, text="Energy:").grid(row=1, column=2, sticky=tk.W, padx=(10, 0))
        self.energy_var = tk.IntVar()
        ttk.Spinbox(weapon_grid, from_=0, to=100, textvariable=self.energy_var, width=10).grid(row=1, column=3, padx=5)
        
        # Mount type
        mount_frame = ttk.Frame(self.weapon_frame)
        mount_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(mount_frame, text="Mount Type:").pack(side=tk.LEFT)
        self.mount_var = tk.StringVar()
        mount_combo = ttk.Combobox(mount_frame, textvariable=self.mount_var,
                                  values=["fixed", "turret", "guided"], width=15)
        mount_combo.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(mount_frame, text="Damage Type:").pack(side=tk.LEFT, padx=(10, 0))
        self.damage_type_var = tk.StringVar()
        damage_combo = ttk.Combobox(mount_frame, textvariable=self.damage_type_var,
                                   values=["energy", "kinetic", "explosive"], width=15)
        damage_combo.pack(side=tk.LEFT, padx=5)
        
        # Defense-specific properties
        self.defense_frame = ttk.LabelFrame(self.editor_frame, text="Defense Properties")
        
        defense_grid = ttk.Frame(self.defense_frame)
        defense_grid.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(defense_grid, text="Shield Boost:").grid(row=0, column=0, sticky=tk.W)
        self.shield_boost_var = tk.IntVar()
        ttk.Spinbox(defense_grid, from_=0, to=1000, textvariable=self.shield_boost_var, width=10).grid(row=0, column=1, padx=5)
        
        ttk.Label(defense_grid, text="Armor Boost:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.armor_boost_var = tk.IntVar()
        ttk.Spinbox(defense_grid, from_=0, to=1000, textvariable=self.armor_boost_var, width=10).grid(row=0, column=3, padx=5)
        
        # Save button
        save_frame = ttk.Frame(self.editor_frame)
        save_frame.pack(fill=tk.X, padx=5, pady=10)
        
        ttk.Button(save_frame, text="Save Outfit", command=self.save_outfit).pack(side=tk.RIGHT)
        ttk.Button(save_frame, text="Export JSON", command=self.export_outfit).pack(side=tk.RIGHT, padx=(0, 10))
        
        # Initially hide category-specific frames
        self.hide_all_category_frames()
        
    def hide_all_category_frames(self):
        """Hide all category-specific frames."""
        self.weapon_frame.pack_forget()
        self.defense_frame.pack_forget()
        
    def on_category_change(self, event=None):
        """Handle category selection change."""
        category = self.category_var.get()
        self.hide_all_category_frames()
        
        if category == "weapons":
            self.weapon_frame.pack(fill=tk.X, padx=5, pady=5)
        elif category == "defense":
            self.defense_frame.pack(fill=tk.X, padx=5, pady=5)
            
    def load_outfits(self):
        """Load existing outfits into the list."""
        self.outfit_listbox.delete(0, tk.END)
        
        try:
            for outfit_id, outfit in OUTFITS_REGISTRY.items():
                display_name = f"{outfit.name} ({outfit.category})"
                self.outfit_listbox.insert(tk.END, display_name)
        except NameError:
            # If OUTFITS_REGISTRY doesn't exist, create some example data
            self.outfit_listbox.insert(tk.END, "No outfits loaded - Check imports")
            
    def on_outfit_select(self, event=None):
        """Handle outfit selection from list."""
        selection = self.outfit_listbox.curselection()
        if not selection:
            return
            
        try:
            outfit_list = list(OUTFITS_REGISTRY.values())
            outfit = outfit_list[selection[0]]
            self.load_outfit_into_editor(outfit)
        except (NameError, IndexError):
            pass
            
    def load_outfit_into_editor(self, outfit):
        """Load an outfit's data into the editor."""
        self.current_outfit = outfit
        
        # Load basic properties
        self.id_var.set(outfit.id)
        self.name_var.set(outfit.name)
        self.category_var.set(outfit.category)
        self.cost_var.set(outfit.cost)
        self.space_var.set(outfit.space_required)
        self.tech_var.set(outfit.min_tech_level)
        
        self.description_text.delete(1.0, tk.END)
        self.description_text.insert(1.0, outfit.description)
        
        # Show appropriate category frame
        self.on_category_change()
        
        # Load category-specific properties
        if isinstance(outfit, Weapon):
            self.damage_var.set(outfit.damage)
            self.fire_rate_var.set(outfit.fire_rate)
            self.range_var.set(outfit.range)
            self.energy_var.set(outfit.energy_usage)
            self.mount_var.set(outfit.mount_type)
            self.damage_type_var.set(outfit.damage_type)
        elif isinstance(outfit, DefenseOutfit):
            self.shield_boost_var.set(outfit.shield_boost)
            self.armor_boost_var.set(outfit.armor_boost)
            
    def new_weapon(self):
        """Create a new weapon outfit."""
        new_id = f"new_weapon_{len(OUTFITS_REGISTRY) + 1}"
        weapon = Weapon(new_id, "New Weapon", "energy")
        weapon.cost = 1000
        weapon.space_required = 1
        weapon.description = "A new weapon"
        
        register_outfit(weapon)
        self.load_outfits()
        self.load_outfit_into_editor(weapon)
        
    def new_defense(self):
        """Create a new defense outfit."""
        new_id = f"new_defense_{len(OUTFITS_REGISTRY) + 1}"
        defense = DefenseOutfit(new_id, "New Defense", "shields")
        defense.cost = 1000
        defense.space_required = 1
        defense.description = "A new defense system"
        
        register_outfit(defense)
        self.load_outfits()
        self.load_outfit_into_editor(defense)
        
    def save_outfit(self):
        """Save the current outfit with editor values."""
        if not self.current_outfit:
            messagebox.showwarning("Warning", "No outfit selected to save")
            return
            
        try:
            # Update basic properties
            old_id = self.current_outfit.id
            self.current_outfit.id = self.id_var.get()
            self.current_outfit.name = self.name_var.get()
            self.current_outfit.category = self.category_var.get()
            self.current_outfit.cost = self.cost_var.get()
            self.current_outfit.space_required = self.space_var.get()
            self.current_outfit.min_tech_level = self.tech_var.get()
            self.current_outfit.description = self.description_text.get(1.0, tk.END).strip()
            
            # Update category-specific properties
            if isinstance(self.current_outfit, Weapon):
                self.current_outfit.damage = self.damage_var.get()
                self.current_outfit.fire_rate = self.fire_rate_var.get()
                self.current_outfit.range = self.range_var.get()
                self.current_outfit.energy_usage = self.energy_var.get()
                self.current_outfit.mount_type = self.mount_var.get()
                self.current_outfit.damage_type = self.damage_type_var.get()
            elif isinstance(self.current_outfit, DefenseOutfit):
                self.current_outfit.shield_boost = self.shield_boost_var.get()
                self.current_outfit.armor_boost = self.armor_boost_var.get()
            
            # Update registry if ID changed
            if old_id != self.current_outfit.id:
                if old_id in OUTFITS_REGISTRY:
                    del OUTFITS_REGISTRY[old_id]
                OUTFITS_REGISTRY[self.current_outfit.id] = self.current_outfit
            
            self.load_outfits()
            messagebox.showinfo("Success", f"Saved outfit: {self.current_outfit.name}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save outfit: {e}")
            
    def delete_outfit(self):
        """Delete the selected outfit."""
        if not self.current_outfit:
            messagebox.showwarning("Warning", "No outfit selected to delete")
            return
            
        result = messagebox.askyesno("Confirm Delete", 
                                   f"Are you sure you want to delete '{self.current_outfit.name}'?")
        if result:
            if self.current_outfit.id in OUTFITS_REGISTRY:
                del OUTFITS_REGISTRY[self.current_outfit.id]
            self.current_outfit = None
            self.load_outfits()
            self.clear_editor()
            
    def clear_editor(self):
        """Clear all editor fields."""
        self.id_var.set("")
        self.name_var.set("")
        self.category_var.set("")
        self.cost_var.set(0)
        self.space_var.set(0)
        self.tech_var.set(1)
        self.description_text.delete(1.0, tk.END)
        self.hide_all_category_frames()
        
    def export_outfit(self):
        """Export current outfit to JSON file."""
        if not self.current_outfit:
            messagebox.showwarning("Warning", "No outfit selected to export")
            return
            
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            initialname=f"{self.current_outfit.id}.json"
        )
        
        if filename:
            try:
                save_outfit_to_json(self.current_outfit, filename)
                messagebox.showinfo("Success", f"Exported outfit to {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export outfit: {e}")

def main():
    """Main function to run the editor."""
    root = tk.Tk()
    app = SimpleEditor(root)
    root.mainloop()

if __name__ == "__main__":
    main()
