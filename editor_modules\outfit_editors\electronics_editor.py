"""
Electronics Editor for the Enhanced Content Editor
Handles editing of electronics outfits
"""

import tkinter as tk
from tkinter import ttk, messagebox
from .base_editor import BaseOutfitEditor

class ElectronicsEditor(BaseOutfitEditor):
    """Editor for electronics outfits."""
    
    def __init__(self, parent, data_manager):
        super().__init__(parent, data_manager, "electronics")
    
    def setup_editor_ui(self, parent):
        """Setup the electronics editor interface."""
        # Basic Properties
        basic_frame = ttk.LabelFrame(parent, text="Basic Properties")
        basic_frame.pack(fill=tk.X, padx=5, pady=5)
        
        row1 = ttk.Frame(basic_frame)
        row1.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(row1, text="Name:").grid(row=0, column=0, sticky=tk.W)
        self.name_var = tk.StringVar()
        ttk.Entry(row1, textvariable=self.name_var, width=25).grid(row=0, column=1, padx=5)
        
        ttk.Label(row1, text="Cost:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.cost_var = tk.IntVar()
        ttk.Spinbox(row1, from_=100, to=100000, textvariable=self.cost_var, width=10).grid(row=0, column=3, padx=5)
        
        row2 = ttk.Frame(basic_frame)
        row2.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(row2, text="Space Required:").grid(row=0, column=0, sticky=tk.W)
        self.space_var = tk.IntVar()
        ttk.Spinbox(row2, from_=1, to=20, textvariable=self.space_var, width=10).grid(row=0, column=1, padx=5)
        
        ttk.Label(row2, text="Type:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.type_var = tk.StringVar()
        ttk.Combobox(row2, textvariable=self.type_var, 
                    values=["sensors", "targeting", "jamming", "communication", "scanning"], width=15).grid(row=0, column=3, padx=5)
        
        # Image Path
        row3 = ttk.Frame(basic_frame)
        row3.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(row3, text="Image Path:").grid(row=0, column=0, sticky=tk.W)
        self.image_var = tk.StringVar()
        ttk.Entry(row3, textvariable=self.image_var, width=35).grid(row=0, column=1, padx=5)
        ttk.Button(row3, text="Browse", command=lambda: self.browse_image(self.image_var)).grid(row=0, column=2, padx=2)
        
        # Electronics Properties
        electronics_frame = ttk.LabelFrame(parent, text="Electronics Properties")
        electronics_frame.pack(fill=tk.X, padx=5, pady=5)
        
        electronics_grid = ttk.Frame(electronics_frame)
        electronics_grid.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(electronics_grid, text="Sensor Range Boost:").grid(row=0, column=0, sticky=tk.W)
        self.sensor_range_var = tk.DoubleVar()
        ttk.Spinbox(electronics_grid, from_=0.0, to=2000.0, increment=10.0, textvariable=self.sensor_range_var, width=10).grid(row=0, column=1, padx=5)
        
        ttk.Label(electronics_grid, text="Targeting Boost:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.targeting_var = tk.DoubleVar()
        ttk.Spinbox(electronics_grid, from_=0.0, to=1.0, increment=0.01, textvariable=self.targeting_var, width=10).grid(row=0, column=3, padx=5)
        
        ttk.Label(electronics_grid, text="Jamming Strength:").grid(row=1, column=0, sticky=tk.W)
        self.jamming_var = tk.DoubleVar()
        ttk.Spinbox(electronics_grid, from_=0.0, to=1.0, increment=0.01, textvariable=self.jamming_var, width=10).grid(row=1, column=1, padx=5)
        
        ttk.Label(electronics_grid, text="Energy Drain:").grid(row=1, column=2, sticky=tk.W, padx=(10, 0))
        self.energy_drain_var = tk.DoubleVar()
        ttk.Spinbox(electronics_grid, from_=0.0, to=50.0, increment=0.1, textvariable=self.energy_drain_var, width=10).grid(row=1, column=3, padx=5)
        
        # Save button
        save_frame = ttk.Frame(parent)
        save_frame.pack(fill=tk.X, padx=5, pady=10)
        
        ttk.Button(save_frame, text="Save Electronics", command=self.save_item).pack(side=tk.RIGHT, padx=5)
    
    def load_item_into_editor(self, electronic):
        """Load electronics data into the editor."""
        super().load_item_into_editor(electronic)
        
        self.name_var.set(electronic.name)
        self.cost_var.set(getattr(electronic, 'cost', 1000))
        self.space_var.set(getattr(electronic, 'space_required', 1))
        self.type_var.set(getattr(electronic, 'subcategory', 'sensors'))
        self.sensor_range_var.set(getattr(electronic, 'sensor_range_boost', 0.0))
        self.targeting_var.set(getattr(electronic, 'targeting_boost', 0.0))
        self.jamming_var.set(getattr(electronic, 'jamming_strength', 0.0))
        self.energy_drain_var.set(getattr(electronic, 'energy_drain', 0.0))
        self.image_var.set(getattr(electronic, 'outfitter_icon', ''))
    
    def save_item(self):
        """Save the current electronics with editor values."""
        if not self.current_outfit:
            messagebox.showwarning("Warning", "No electronics selected to save")
            return
        
        try:
            self.current_outfit.name = self.name_var.get()
            if hasattr(self.current_outfit, 'cost'):
                self.current_outfit.cost = self.cost_var.get()
            if hasattr(self.current_outfit, 'space_required'):
                self.current_outfit.space_required = self.space_var.get()
            if hasattr(self.current_outfit, 'subcategory'):
                self.current_outfit.subcategory = self.type_var.get()
            if hasattr(self.current_outfit, 'sensor_range_boost'):
                self.current_outfit.sensor_range_boost = self.sensor_range_var.get()
            if hasattr(self.current_outfit, 'targeting_boost'):
                self.current_outfit.targeting_boost = self.targeting_var.get()
            if hasattr(self.current_outfit, 'jamming_strength'):
                self.current_outfit.jamming_strength = self.jamming_var.get()
            if hasattr(self.current_outfit, 'energy_drain'):
                self.current_outfit.energy_drain = self.energy_drain_var.get()
            if hasattr(self.current_outfit, 'outfitter_icon'):
                self.current_outfit.outfitter_icon = self.image_var.get()
            
            super().save_item()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save electronics: {e}")
    
    def create_new_item_instance(self, item_id):
        """Create a new electronics instance."""
        class SimpleElectronics:
            def __init__(self, id, name):
                self.id = id
                self.name = name
                self.category = "electronics"
                self.subcategory = "sensors"
                self.cost = 1000
                self.space_required = 1
                self.sensor_range_boost = 0.0
                self.targeting_boost = 0.0
                self.jamming_strength = 0.0
                self.energy_drain = 0.0
                self.outfitter_icon = ""
        
        return SimpleElectronics(item_id, item_id.replace('_', ' ').title())
