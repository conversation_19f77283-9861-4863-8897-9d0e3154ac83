"""
AI Combat Module - Enhanced combat logic for AI ships
This module handles weapon selection, targeting, and combat tactics.
"""
import pygame as pg
import random
import math
from game_objects.projectiles import LaserProjectile, MissileProjectile
from game_objects.standardized_outfits import *

class AICombatManager:
    """
    Manages combat behavior for AI ships.
    Handles weapon selection, targeting, and tactical decisions.
    """
    
    def __init__(self, ai_ship):
        self.ship = ai_ship
        self.last_weapon_switch = 0
        self.weapon_switch_cooldown = 120  # 2 seconds at 60 FPS
        self.missile_conservation_threshold = 0.3  # Use missiles when ammo > 30%
        
    def select_best_weapon(self, target, distance):
        """
        Select the most appropriate weapon for the current situation.
        
        Args:
            target: The target entity
            distance: Distance to target
            
        Returns:
            int: Index of best weapon, or -1 if no suitable weapon
        """
        if not self.ship.weapons:
            return -1
        
        best_weapon_index = -1
        best_score = -1
        
        for i, weapon in enumerate(self.ship.weapons):
            score = self._evaluate_weapon_effectiveness(weapon, target, distance)
            if score > best_score:
                best_score = score
                best_weapon_index = i
        
        return best_weapon_index
    
    def _evaluate_weapon_effectiveness(self, weapon, target, distance):
        """
        Evaluate how effective a weapon would be in the current situation.
        
        Args:
            weapon: The weapon to evaluate
            target: The target entity
            distance: Distance to target
            
        Returns:
            float: Effectiveness score (higher is better)
        """
        if not weapon.can_fire():
            return 0
        
        # Base effectiveness from damage and fire rate
        base_effectiveness = weapon.damage * weapon.fire_rate
        
        # Range penalty - weapons are less effective outside optimal range
        range_factor = 1.0
        if distance > weapon.range:
            return 0  # Can't hit at all
        elif distance > weapon.range * 0.8:
            range_factor = 0.5  # Reduced effectiveness at long range
        
        # Mount type considerations
        mount_factor = 1.0
        if weapon.mount_type == MOUNT_TYPE_FIXED:
            # Fixed weapons need to be pointing at target
            target_angle = self.ship.get_angle_to_point(target.pos)
            angle_diff = abs((self.ship.angle - target_angle) % 360)
            if angle_diff > 180:
                angle_diff = 360 - angle_diff
            
            if angle_diff > 30:  # Not pointing at target
                mount_factor = 0.1
            elif angle_diff > 15:
                mount_factor = 0.5
        elif weapon.mount_type == MOUNT_TYPE_TURRET:
            # Turrets are always effective if in range
            mount_factor = 1.2  # Slight bonus for flexibility
        
        # Ammo considerations for missile weapons
        ammo_factor = 1.0
        if weapon.uses_ammo:
            if weapon.current_ammo <= 0:
                return 0  # No ammo
            
            ammo_ratio = weapon.current_ammo / weapon.max_ammo
            if ammo_ratio < self.missile_conservation_threshold:
                ammo_factor = 0.3  # Conserve ammo when low
            else:
                ammo_factor = 1.5  # Bonus for missile weapons when ammo is available
        
        return base_effectiveness * range_factor * mount_factor * ammo_factor
    
    def fire_weapon(self, dt):
        """
        Fire the currently active weapon if possible.
        
        Args:
            dt: Delta time
            
        Returns:
            Projectile or None if no weapon fired
        """
        if not self.ship.weapons or self.ship.active_weapon_index >= len(self.ship.weapons):
            return None
        
        weapon = self.ship.weapons[self.ship.active_weapon_index]
        weapon.update(dt)
        
        if not weapon.can_fire():
            return None
        
        target = self.ship.target_entity
        if weapon.mount_type in [MOUNT_TYPE_TURRET, MOUNT_TYPE_GUIDED] and not target:
            return None
        
        # Calculate spawn position and angle
        spawn_pos = self.ship.pos.copy()
        projectile_angle = self.ship.angle
        
        forward_angle = math.radians(self.ship.angle - 90)
        forward = pg.math.Vector2(math.cos(forward_angle), math.sin(forward_angle))
        
        if weapon.mount_type == MOUNT_TYPE_FIXED:
            offset_to_nose = self.ship.image_orig.get_height() / 2
            spawn_pos = self.ship.pos + forward * offset_to_nose
        elif weapon.mount_type == MOUNT_TYPE_TURRET and target:
            to_target = target.pos - self.ship.pos
            target_angle = math.degrees(math.atan2(to_target.y, to_target.x)) + 90
            projectile_angle = target_angle % 360
            to_target.normalize()
            spawn_pos = self.ship.pos + to_target * (self.ship.rect.width / 3)
        elif weapon.mount_type == MOUNT_TYPE_GUIDED and target:
            spawn_pos = self.ship.pos + forward * (self.ship.rect.height / 2)
            projectile_angle = self.ship.angle
        
        # Create projectile based on weapon behavior
        projectile = None
        if weapon.projectile_behavior == BEHAVIOR_INSTANT:
            projectile = LaserProjectile(
                self.ship.game,
                self.ship,
                spawn_pos,
                projectile_angle,
                weapon.damage,
                weapon.range
            )
        elif weapon.projectile_behavior == BEHAVIOR_BEAM:
            projectile = LaserProjectile(
                self.ship.game,
                self.ship,
                spawn_pos,
                projectile_angle,
                weapon.damage,
                weapon.range,
                beam=True
            )
        elif weapon.uses_ammo:
            projectile = MissileProjectile(
                self.ship.game,
                self.ship,
                spawn_pos,
                projectile_angle,
                weapon.damage,
                weapon.range,
                target,
                tracking=getattr(weapon, 'tracking_strength', 0.0)
            )
            weapon.current_ammo -= 1
        else:
            projectile = LaserProjectile(
                self.ship.game,
                self.ship,
                spawn_pos,
                projectile_angle,
                weapon.damage,
                weapon.range
            )
        
        if projectile and weapon.fire():
            self.ship.game.all_sprites.add(projectile)
            self.ship.projectiles.add(projectile)
            return projectile
        
        return None
    
    def should_switch_weapons(self, target, distance):
        """
        Determine if the AI should switch to a different weapon.
        
        Args:
            target: Current target
            distance: Distance to target
            
        Returns:
            bool: True if should switch weapons
        """
        # Don't switch too frequently
        if self.ship.game.frame_count - self.last_weapon_switch < self.weapon_switch_cooldown:
            return False
        
        if not self.ship.weapons or len(self.ship.weapons) <= 1:
            return False
        
        current_weapon = self.ship.weapons[self.ship.active_weapon_index]
        current_effectiveness = self._evaluate_weapon_effectiveness(current_weapon, target, distance)
        
        # Find best alternative weapon
        best_index = self.select_best_weapon(target, distance)
        if best_index == -1 or best_index == self.ship.active_weapon_index:
            return False
        
        best_weapon = self.ship.weapons[best_index]
        best_effectiveness = self._evaluate_weapon_effectiveness(best_weapon, target, distance)
        
        # Switch if alternative is significantly better
        return best_effectiveness > current_effectiveness * 1.3
    
    def switch_to_best_weapon(self, target, distance):
        """
        Switch to the most effective weapon for the current situation.
        
        Args:
            target: Current target
            distance: Distance to target
        """
        best_index = self.select_best_weapon(target, distance)
        if best_index != -1 and best_index != self.ship.active_weapon_index:
            self.ship.active_weapon_index = best_index
            self.last_weapon_switch = self.ship.game.frame_count
            weapon = self.ship.weapons[best_index]
            print(f"AI Ship {self.ship.ship_type} switched to {weapon.name}")
    
    def get_optimal_engagement_range(self):
        """
        Get the optimal engagement range based on equipped weapons.
        
        Returns:
            float: Optimal engagement distance
        """
        if not self.ship.weapons:
            return 150
        
        # Prioritize based on weapon types
        if self.ship.has_missile_weapons:
            return 250  # Stay at medium range for missiles
        elif self.ship.has_turret_weapons and not self.ship.has_fixed_weapons:
            return 180  # Turrets can engage at medium range
        elif self.ship.has_fixed_weapons:
            return 120  # Get close for fixed weapons
        else:
            return 150  # Default range
