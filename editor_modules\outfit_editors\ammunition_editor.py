"""
Ammunition Editor for the Enhanced Content Editor
Handles editing of ammunition outfits
"""

import tkinter as tk
from tkinter import ttk, messagebox
from .base_editor import BaseOutfitEditor

class AmmunitionEditor(BaseOutfitEditor):
    """Editor for ammunition outfits."""
    
    def __init__(self, parent, data_manager):
        super().__init__(parent, data_manager, "ammunition")
    
    def setup_editor_ui(self, parent):
        """Setup the ammunition editor interface."""
        # Basic Properties
        basic_frame = ttk.LabelFrame(parent, text="Basic Properties")
        basic_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Name, Type, Cost
        row1 = ttk.Frame(basic_frame)
        row1.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(row1, text="Name:").grid(row=0, column=0, sticky=tk.W)
        self.name_var = tk.StringVar()
        ttk.Entry(row1, textvariable=self.name_var, width=25).grid(row=0, column=1, padx=5)
        
        ttk.Label(row1, text="Cost:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.cost_var = tk.IntVar()
        ttk.Spinbox(row1, from_=100, to=10000, textvariable=self.cost_var, width=10).grid(row=0, column=3, padx=5)
        
        # Ammo Type and Quantity
        row2 = ttk.Frame(basic_frame)
        row2.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(row2, text="Ammo Type:").grid(row=0, column=0, sticky=tk.W)
        self.ammo_type_var = tk.StringVar()
        ttk.Combobox(row2, textvariable=self.ammo_type_var, 
                    values=["light_missile", "heavy_missile", "torpedo", "rocket"], width=15).grid(row=0, column=1, padx=5)
        
        ttk.Label(row2, text="Quantity:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.quantity_var = tk.IntVar()
        ttk.Spinbox(row2, from_=1, to=100, textvariable=self.quantity_var, width=10).grid(row=0, column=3, padx=5)
        
        # Projectile Properties
        projectile_frame = ttk.LabelFrame(parent, text="Projectile Properties")
        projectile_frame.pack(fill=tk.X, padx=5, pady=5)
        
        proj_grid = ttk.Frame(projectile_frame)
        proj_grid.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(proj_grid, text="Damage:").grid(row=0, column=0, sticky=tk.W)
        self.damage_var = tk.IntVar()
        ttk.Spinbox(proj_grid, from_=1, to=1000, textvariable=self.damage_var, width=10).grid(row=0, column=1, padx=5)
        
        ttk.Label(proj_grid, text="Speed:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.speed_var = tk.IntVar()
        ttk.Spinbox(proj_grid, from_=1, to=50, textvariable=self.speed_var, width=10).grid(row=0, column=3, padx=5)
        
        # Behavior and Tracking
        ttk.Label(proj_grid, text="Behavior:").grid(row=1, column=0, sticky=tk.W)
        self.behavior_var = tk.StringVar()
        ttk.Combobox(proj_grid, textvariable=self.behavior_var, 
                    values=["instant", "dumbfire", "guided", "beam", "delayed", "proximity"], width=15).grid(row=1, column=1, padx=5)
        
        ttk.Label(proj_grid, text="Tracking (0.0-1.0):").grid(row=1, column=2, sticky=tk.W, padx=(10, 0))
        self.tracking_var = tk.DoubleVar()
        ttk.Spinbox(proj_grid, from_=0.0, to=1.0, increment=0.1, textvariable=self.tracking_var, width=10).grid(row=1, column=3, padx=5)
        
        # Explosion Properties
        explosion_frame = ttk.LabelFrame(parent, text="Explosion Properties")
        explosion_frame.pack(fill=tk.X, padx=5, pady=5)
        
        exp_grid = ttk.Frame(explosion_frame)
        exp_grid.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(exp_grid, text="Explosion Radius:").grid(row=0, column=0, sticky=tk.W)
        self.explosion_var = tk.IntVar()
        ttk.Spinbox(exp_grid, from_=0, to=100, textvariable=self.explosion_var, width=10).grid(row=0, column=1, padx=5)
        
        # Save button
        save_frame = ttk.Frame(parent)
        save_frame.pack(fill=tk.X, padx=5, pady=10)
        
        ttk.Button(save_frame, text="Save Ammunition", command=self.save_item).pack(side=tk.RIGHT, padx=5)
    
    def load_item_into_editor(self, ammo):
        """Load ammunition data into the editor."""
        super().load_item_into_editor(ammo)
        
        # Load basic properties
        self.name_var.set(ammo.name)
        self.cost_var.set(getattr(ammo, 'cost', 500))
        self.ammo_type_var.set(getattr(ammo, 'ammo_type', ''))
        self.quantity_var.set(getattr(ammo, 'quantity', 10))
        
        # Load projectile properties
        self.damage_var.set(getattr(ammo, 'damage', 50))
        self.speed_var.set(getattr(ammo, 'projectile_speed', 8))
        self.behavior_var.set(getattr(ammo, 'projectile_behavior', 'dumbfire'))
        self.tracking_var.set(getattr(ammo, 'tracking_strength', 0.0))
        self.explosion_var.set(getattr(ammo, 'explosion_radius', 20))
    
    def save_item(self):
        """Save the current ammunition with editor values."""
        if not self.current_outfit:
            messagebox.showwarning("Warning", "No ammunition selected to save")
            return
        
        try:
            # Update basic properties
            self.current_outfit.name = self.name_var.get()
            if hasattr(self.current_outfit, 'cost'):
                self.current_outfit.cost = self.cost_var.get()
            if hasattr(self.current_outfit, 'ammo_type'):
                self.current_outfit.ammo_type = self.ammo_type_var.get()
            if hasattr(self.current_outfit, 'quantity'):
                self.current_outfit.quantity = self.quantity_var.get()
            
            # Update projectile properties
            if hasattr(self.current_outfit, 'damage'):
                self.current_outfit.damage = self.damage_var.get()
            if hasattr(self.current_outfit, 'projectile_speed'):
                self.current_outfit.projectile_speed = self.speed_var.get()
            if hasattr(self.current_outfit, 'projectile_behavior'):
                self.current_outfit.projectile_behavior = self.behavior_var.get()
            if hasattr(self.current_outfit, 'tracking_strength'):
                self.current_outfit.tracking_strength = self.tracking_var.get()
            if hasattr(self.current_outfit, 'explosion_radius'):
                self.current_outfit.explosion_radius = self.explosion_var.get()
            
            super().save_item()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save ammunition: {e}")
    
    def create_new_item_instance(self, item_id):
        """Create a new ammunition instance."""
        try:
            # Try to use the standardized system if available
            import sys
            if 'game_objects.standardized_outfits' in sys.modules:
                from game_objects.standardized_outfits import AmmunitionOutfit
                return AmmunitionOutfit(item_id, item_id.replace('_', ' ').title(), "light_missile")
            else:
                # Create a simple object if classes aren't available
                class SimpleAmmo:
                    def __init__(self, id, name):
                        self.id = id
                        self.name = name
                        self.category = "ammunition"
                        self.ammo_type = "light_missile"
                        self.cost = 500
                        self.quantity = 10
                        self.damage = 50
                        self.projectile_speed = 8
                        self.projectile_behavior = "dumbfire"
                        self.tracking_strength = 0.0
                        self.explosion_radius = 20
                
                return SimpleAmmo(item_id, item_id.replace('_', ' ').title())
        except ImportError:
            # Fallback to simple object
            class SimpleAmmo:
                def __init__(self, id, name):
                    self.id = id
                    self.name = name
                    self.category = "ammunition"
                    self.ammo_type = "light_missile"
                    self.cost = 500
                    self.quantity = 10
                    self.damage = 50
                    self.projectile_speed = 8
                    self.projectile_behavior = "dumbfire"
                    self.tracking_strength = 0.0
                    self.explosion_radius = 20
            
            return SimpleAmmo(item_id, item_id.replace('_', ' ').title())
