"""
Outfit definitions for Escape Velocity Py.
This module contains all outfits that can be installed on ships.
"""

# Outfit categories
CATEGORY_WEAPON = "weapon"
CATEGORY_ENGINE = "engine"
CATEGORY_SHIELD = "shield"
CATEGORY_ARMOR = "armor"
CATEGORY_UTILITY = "utility"
CATEGORY_SPECIAL = "special"
CATEGORY_AMMO = "ammo"

# Weapon types
WEAPON_TYPE_LASER = "laser"
WEAPON_TYPE_MISSILE = "missile"
WEAPON_TYPE_PROJECTILE = "projectile"
WEAPON_TYPE_BEAM = "beam"

# Weapon mount types
MOUNT_TYPE_FIXED = "fixed"      # Fixed forward-firing weapons
MOUNT_TYPE_TURRET = "turret"    # Turret weapons that can target independently
MOUNT_TYPE_GUIDED = "guided"    # Guided weapons that track targets

# Tech levels
TECH_LEVEL_MIN = 0
TECH_LEVEL_MAX = 5

# Ship size compatibility
from game_objects.ships import SIZE_SMALL, SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL
import os
import pygame as pg
import copy # For shallow copying lists if needed

class Outfit:
    """Base class for all ship outfits."""

    def __init__(self, name, category, space_required, cost,
                 compatible_sizes=None, min_tech_level=1, description="", image_path=None):
        """
        Initialize an outfit with its specifications.

        Args:
            name (str): The name of the outfit
            category (str): The category of the outfit (weapon, engine, etc.)
            space_required (int): Space required in tons
            cost (int): Cost in credits
            compatible_sizes (list, optional): List of ship sizes this outfit is compatible with.
                                              If None, compatible with all sizes.
            min_tech_level (int, optional): Minimum tech level required to purchase this outfit.
            description (str, optional): Outfit description. Defaults to "".
            image_path (str, optional): Path to the outfit's image. Defaults to None.
        """
        self.name = name
        self.category = category
        self.space_required = space_required
        self.cost = cost
        self.compatible_sizes = compatible_sizes or [SIZE_SMALL, SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL]
        self.min_tech_level = min_tech_level
        self.description = description
        self.image_path = image_path
        self.image = None

        # Load the image if provided
        if image_path and os.path.exists(image_path):
            try:
                self.image = pg.image.load(image_path).convert_alpha()
            except:
                print(f"Failed to load image for {name}: {image_path}")
                self.create_default_image()
        else:
            self.create_default_image()

    def create_default_image(self):
        """Create a default image for the outfit."""
        # Create a small colored square based on outfit category
        size = 16  # Small icon size
        self.image = pg.Surface([size, size], pg.SRCALPHA)

        # Different colors for different categories
        colors = {
            CATEGORY_WEAPON: (255, 0, 0),      # Red
            CATEGORY_ENGINE: (0, 255, 0),      # Green
            CATEGORY_SHIELD: (0, 0, 255),      # Blue
            CATEGORY_ARMOR: (128, 128, 128),   # Gray
            CATEGORY_UTILITY: (255, 255, 0),   # Yellow
            CATEGORY_SPECIAL: (255, 0, 255),   # Magenta
            CATEGORY_AMMO: (255, 128, 0)       # Orange
        }

        color = colors.get(self.category, (200, 200, 200))  # Default to light gray
        pg.draw.rect(self.image, color, (0, 0, size, size))

    def is_compatible_with_size(self, ship_size):
        """Check if this outfit is compatible with a given ship size."""
        return ship_size in self.compatible_sizes

    def __str__(self):
        return f"{self.name} ({self.category}, {self.space_required} tons)"

    def clone(self):
        """Create a new instance of this outfit with the same initial parameters."""
        # Subclasses should override this to call their own __init__ with all necessary parameters.
        # This base implementation is a fallback and might not be sufficient for all subclasses.
        cloned = self.__class__(
            name=self.name,
            category=self.category,
            space_required=self.space_required,
            cost=self.cost,
            compatible_sizes=copy.copy(self.compatible_sizes) if self.compatible_sizes else None,
            min_tech_level=self.min_tech_level,
            description=self.description,
            image_path=self.image_path
        )
        return cloned


class Weapon(Outfit):
    """Weapon outfit for ships."""

    def __init__(self, name, space_required, cost, damage, fire_rate,
                 energy_usage, range, weapon_type=WEAPON_TYPE_LASER,
                 mount_type=MOUNT_TYPE_FIXED, accuracy=0.9, spread=0,
                 compatible_sizes=None, min_tech_level=1, description="", image_path=None):
        """
        Initialize a weapon outfit.

        Additional Args:
            damage (float): Damage per shot
            fire_rate (float): Shots per second
            energy_usage (float): Energy used per shot
            range (float): Maximum range
            weapon_type (str): Type of weapon (laser, missile, etc.)
            mount_type (str): Type of mount (fixed, turret, guided)
            accuracy (float): Accuracy of the weapon (0.0-1.0)
            spread (float): Spread of the weapon in degrees
        """
        super().__init__(name, CATEGORY_WEAPON, space_required, cost,
                         compatible_sizes, min_tech_level, description, image_path)
        self.damage = damage
        self.fire_rate = fire_rate
        self.energy_usage = energy_usage
        self.range = range
        self.weapon_type = weapon_type
        self.mount_type = mount_type
        self.accuracy = accuracy
        self.spread = spread
        self.dps = damage * fire_rate  # Damage per second
        self.cooldown = 0  # Current cooldown timer
        self.cooldown_time = 1.0 / fire_rate if fire_rate > 0 else 1.0  # Time between shots

    def can_fire(self):
        """Check if the weapon is ready to fire."""
        return self.cooldown <= 0

    def update(self, dt):
        """Update weapon cooldown."""
        if self.cooldown > 0:
            self.cooldown -= dt

    def fire(self, owner, target_pos=None):
        """
        Fire the weapon.

        Args:
            owner: The entity firing the weapon
            target_pos: Optional target position for guided weapons

        Returns:
            Projectile or None if can't fire
        """
        if not self.can_fire():
            return None

        # Reset cooldown
        self.cooldown = self.cooldown_time

        # Base implementation - override in subclasses
        return None

    def clone(self):
        cloned_weapon = Weapon(
            name=self.name,
            space_required=self.space_required,
            cost=self.cost,
            damage=self.damage,
            fire_rate=self.fire_rate,
            energy_usage=self.energy_usage,
            range=self.range,
            weapon_type=self.weapon_type,
            mount_type=self.mount_type,
            accuracy=self.accuracy,
            spread=self.spread,
            compatible_sizes=copy.copy(self.compatible_sizes) if self.compatible_sizes else None,
            min_tech_level=self.min_tech_level,
            description=self.description,
            image_path=self.image_path
        )
        # Cooldown is reset by Weapon.__init__
        return cloned_weapon


class MissileWeapon(Weapon):
    """Missile launcher that requires ammunition."""

    def __init__(self, name, space_required, cost, damage, fire_rate,
                 energy_usage, range, ammo_type, max_ammo=10,
                 mount_type=MOUNT_TYPE_GUIDED, accuracy=0.9, tracking=0.8,
                 compatible_sizes=None, min_tech_level=2, description="", image_path=None):
        """
        Initialize a missile weapon.

        Additional Args:
            ammo_type (str): Type of ammunition this weapon uses
            max_ammo (int): Maximum ammunition capacity
            tracking (float): How well the missile tracks its target (0.0-1.0)
        """
        super().__init__(name, space_required, cost, damage, fire_rate,
                         energy_usage, range, WEAPON_TYPE_MISSILE,
                         mount_type, accuracy, 0,  # Missiles don't have spread
                         compatible_sizes, min_tech_level, description, image_path)
        self.ammo_type = ammo_type
        self.max_ammo = max_ammo
        self.current_ammo = 0  # Start with no ammo
        self.tracking = tracking  # How well the missile tracks its target

    def can_fire(self):
        """Check if the missile launcher can fire."""
        return super().can_fire() and self.current_ammo > 0

    def add_ammo(self, amount):
        """Add ammunition to the launcher."""
        self.current_ammo = min(self.current_ammo + amount, self.max_ammo)
        return self.current_ammo

    def fire(self, owner, target=None):
        """
        Fire a missile.

        Args:
            owner: The entity firing the missile
            target: The target entity for guided missiles

        Returns:
            Missile projectile or None if can't fire
        """
        if not self.can_fire() or target is None:
            return None

        # Use ammo and reset cooldown
        self.current_ammo -= 1
        self.cooldown = self.cooldown_time

        # Missile creation will be handled by the projectile system
        # This is a placeholder until we implement the projectile class
        print(f"Fired missile at {target}")
        return None  # Will return a Missile object in the future

    def clone(self):
        cloned_missile_weapon = MissileWeapon(
            name=self.name,
            space_required=self.space_required,
            cost=self.cost,
            damage=self.damage,
            fire_rate=self.fire_rate,
            energy_usage=self.energy_usage,
            range=self.range,
            ammo_type=self.ammo_type,
            max_ammo=self.max_ammo,
            mount_type=self.mount_type,
            accuracy=self.accuracy,
            tracking=self.tracking,
            compatible_sizes=copy.copy(self.compatible_sizes) if self.compatible_sizes else None,
            min_tech_level=self.min_tech_level,
            description=self.description,
            image_path=self.image_path
        )
        # current_ammo and cooldown are reset by MissileWeapon.__init__
        return cloned_missile_weapon


class Ammunition(Outfit):
    """Ammunition for missile weapons."""

    def __init__(self, name, ammo_type, quantity, cost,
                 compatible_sizes=None, min_tech_level=1, description="", image_path=None):
        """
        Initialize ammunition.

        Args:
            ammo_type (str): Type of ammunition
            quantity (int): Number of missiles in this package
        """
        super().__init__(name, CATEGORY_AMMO, 0, cost, compatible_sizes, min_tech_level, description, image_path)
        self.ammo_type = ammo_type
        self.quantity = quantity


class Engine(Outfit):
    """Engine outfit for ships."""

    def __init__(self, name, space_required, cost, thrust_boost,
                 turn_boost, energy_usage, compatible_sizes=None, min_tech_level=1, description="", image_path=None):
        """
        Initialize an engine outfit.

        Additional Args:
            thrust_boost (float): Boost to ship's acceleration
            turn_boost (float): Boost to ship's turn rate
            energy_usage (float): Energy used per second
        """
        super().__init__(name, CATEGORY_ENGINE, space_required, cost, compatible_sizes, min_tech_level, description, image_path)
        self.thrust_boost = thrust_boost
        self.turn_boost = turn_boost
        self.energy_usage = energy_usage


class Shield(Outfit):
    """Shield outfit for ships."""

    def __init__(self, name, space_required, cost, shield_boost,
                 recharge_rate, energy_usage, compatible_sizes=None, min_tech_level=1, description=""):
        """
        Initialize a shield outfit.

        Additional Args:
            shield_boost (float): Additional shield points
            recharge_rate (float): Shield points recharged per second
            energy_usage (float): Energy used per second
        """
        super().__init__(name, CATEGORY_SHIELD, space_required, cost, compatible_sizes, min_tech_level, description)
        self.shield_boost = shield_boost
        self.recharge_rate = recharge_rate
        self.energy_usage = energy_usage


class Armor(Outfit):
    """Armor outfit for ships."""

    def __init__(self, name, space_required, cost, armor_boost,
                 mass_penalty, compatible_sizes=None, min_tech_level=1, description=""):
        """
        Initialize an armor outfit.

        Additional Args:
            armor_boost (float): Additional armor points
            mass_penalty (float): Penalty to ship's acceleration and turn rate
        """
        super().__init__(name, CATEGORY_ARMOR, space_required, cost, compatible_sizes, min_tech_level, description)
        self.armor_boost = armor_boost
        self.mass_penalty = mass_penalty


class Utility(Outfit):
    """Utility outfit for ships."""

    def __init__(self, name, space_required, cost, effect,
                 energy_usage=0, compatible_sizes=None, min_tech_level=1, description="", image_path=None):
        """
        Initialize a utility outfit.

        Additional Args:
            effect (dict): Dictionary of effects this utility provides
            energy_usage (float): Energy used per second
        """
        super().__init__(name, CATEGORY_UTILITY, space_required, cost,
                         compatible_sizes, min_tech_level, description, image_path)
        self.effect = effect
        self.energy_usage = energy_usage


class Special(Outfit):
    """Special outfit for ships with unique effects."""

    def __init__(self, name, space_required, cost, effects,
                 energy_usage=0, compatible_sizes=None, min_tech_level=1, description="", image_path=None):
        """
        Initialize a special outfit.

        Additional Args:
            effects (dict): Dictionary of special effects
            energy_usage (float): Energy used when activated
        """
        super().__init__(name, CATEGORY_SPECIAL, space_required, cost,
                         compatible_sizes, min_tech_level, description, image_path)
        self.effects = effects
        self.energy_usage = energy_usage


# Define all outfits
OUTFITS = {
    # Weapons - Lasers
    "laser_cannon": Weapon(
        name="Laser Cannon",
        space_required=2,
        cost=5000,
        damage=10,
        fire_rate=1.0,  # Reduced from 2.0 to slow down fire rate
        energy_usage=5,
        range=500,
        weapon_type=WEAPON_TYPE_LASER,
        mount_type=MOUNT_TYPE_FIXED,  # Fixed forward-firing weapon
        accuracy=0.95,
        compatible_sizes=[SIZE_SMALL, SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL],
        min_tech_level=1,
        description="A basic laser weapon with decent damage and range. Fixed forward-firing mount."
    ),

    "pulse_laser": Weapon(
        name="Pulse Laser",
        space_required=3,
        cost=8000,
        damage=15,
        fire_rate=1.5,
        energy_usage=8,
        range=450,
        weapon_type=WEAPON_TYPE_LASER,
        mount_type=MOUNT_TYPE_FIXED,  # Fixed forward-firing weapon
        accuracy=0.9,
        compatible_sizes=[SIZE_SMALL, SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL],
        min_tech_level=2,
        description="A more powerful laser with higher damage but slower fire rate. Fixed forward-firing mount."
    ),

    "beam_laser": Weapon(
        name="Beam Laser",
        space_required=5,
        cost=15000,
        damage=25,
        fire_rate=1.0,
        energy_usage=15,
        range=600,
        weapon_type=WEAPON_TYPE_BEAM,
        mount_type=MOUNT_TYPE_FIXED,  # Fixed forward-firing weapon
        accuracy=0.98,
        compatible_sizes=[SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL],
        min_tech_level=3,
        description="A heavy laser weapon with high damage and good range. Fixed forward-firing mount."
    ),

    "turret_laser": Weapon(
        name="Turret Laser",
        space_required=4,
        cost=12000,
        damage=12,
        fire_rate=1.2,
        energy_usage=7,
        range=450,
        weapon_type=WEAPON_TYPE_LASER,
        mount_type=MOUNT_TYPE_TURRET,  # Turret mount that can target independently
        accuracy=0.85,
        compatible_sizes=[SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL],
        min_tech_level=2,
        description="A laser mounted on a turret that can target independently of ship orientation."
    ),

    # Weapons - Projectiles
    "plasma_cannon": Weapon(
        name="Plasma Cannon",
        space_required=6,
        cost=20000,
        damage=40,
        fire_rate=0.8,
        energy_usage=18,
        range=400,
        weapon_type=WEAPON_TYPE_PROJECTILE,
        mount_type=MOUNT_TYPE_FIXED,  # Fixed forward-firing weapon
        accuracy=0.9,
        compatible_sizes=[SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL],
        min_tech_level=3,
        description="Fires superheated plasma bolts with high damage. Fixed forward-firing mount."
    ),

    "plasma_turret": Weapon(
        name="Plasma Turret",
        space_required=8,
        cost=25000,
        damage=35,
        fire_rate=0.7,
        energy_usage=20,
        range=380,
        weapon_type=WEAPON_TYPE_PROJECTILE,
        mount_type=MOUNT_TYPE_TURRET,  # Turret mount that can target independently
        accuracy=0.8,
        compatible_sizes=[SIZE_LARGE, SIZE_CAPITAL],
        min_tech_level=3,
        description="A plasma weapon mounted on a turret that can target independently of ship orientation."
    ),

    "railgun": Weapon(
        name="Railgun",
        space_required=10,
        cost=35000,
        damage=80,
        fire_rate=0.3,
        energy_usage=25,
        range=1200,
        weapon_type=WEAPON_TYPE_PROJECTILE,
        mount_type=MOUNT_TYPE_FIXED,  # Fixed forward-firing weapon
        accuracy=0.98,
        compatible_sizes=[SIZE_LARGE, SIZE_CAPITAL],
        min_tech_level=4,
        description="Electromagnetic projectile weapon with extreme range and damage. Fixed forward-firing mount."
    ),

    # Weapons - Missiles
    "missile_launcher": MissileWeapon(
        name="Missile Launcher",
        space_required=4,
        cost=12000,
        damage=50,
        fire_rate=0.5,
        energy_usage=10,
        range=800,
        ammo_type="standard_missile",
        max_ammo=8,
        mount_type=MOUNT_TYPE_GUIDED,
        accuracy=0.9,
        tracking=0.8,
        compatible_sizes=[SIZE_SMALL, SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL],
        min_tech_level=2,
        description="Launches guided missiles with high damage but slow fire rate. Requires missile ammunition and a target lock."
    ),

    "torpedo_launcher": MissileWeapon(
        name="Torpedo Launcher",
        space_required=8,
        cost=25000,
        damage=100,
        fire_rate=0.2,
        energy_usage=20,
        range=1000,
        ammo_type="torpedo",
        max_ammo=4,
        mount_type=MOUNT_TYPE_GUIDED,
        accuracy=0.95,
        tracking=0.7,  # Slower tracking but more damage
        compatible_sizes=[SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL],
        min_tech_level=3,
        description="Heavy torpedoes with massive damage but very slow fire rate and tracking. Requires torpedo ammunition and a target lock."
    ),

    "swarm_launcher": MissileWeapon(
        name="Swarm Missile Launcher",
        space_required=6,
        cost=18000,
        damage=15,
        fire_rate=2.0,
        energy_usage=12,
        range=600,
        ammo_type="swarm_missile",
        max_ammo=16,
        mount_type=MOUNT_TYPE_GUIDED,
        accuracy=0.85,
        tracking=0.9,  # Fast tracking but less damage
        compatible_sizes=[SIZE_SMALL, SIZE_MEDIUM, SIZE_LARGE],
        min_tech_level=3,
        description="Rapid-fire missile launcher that fires smaller, less damaging missiles with excellent tracking. Requires missile ammunition and a target lock."
    ),

    # Ammunition
    "standard_missiles": Ammunition(
        name="Standard Missiles",
        ammo_type="standard_missile",
        quantity=4,
        cost=2000,
        compatible_sizes=[SIZE_SMALL, SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL],
        min_tech_level=2,
        description="Standard guided missiles for missile launchers."
    ),

    "torpedoes": Ammunition(
        name="Torpedoes",
        ammo_type="torpedo",
        quantity=2,
        cost=3000,
        compatible_sizes=[SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL],
        min_tech_level=3,
        description="Heavy torpedoes with high damage for torpedo launchers."
    ),

    "swarm_missiles": Ammunition(
        name="Swarm Missiles",
        ammo_type="swarm_missile",
        quantity=8,
        cost=2400,
        compatible_sizes=[SIZE_SMALL, SIZE_MEDIUM, SIZE_LARGE],
        min_tech_level=3,
        description="Small, fast missiles for swarm launchers."
    ),

    # Engines
    "standard_engine": Engine(
        name="Standard Engine",
        space_required=5,
        cost=10000,
        thrust_boost=0.2,
        turn_boost=0.1,
        energy_usage=5,
        compatible_sizes=[SIZE_SMALL, SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL],
        min_tech_level=1,
        description="A basic engine providing modest thrust and turning ability."
    ),

    "enhanced_engine": Engine(
        name="Enhanced Engine",
        space_required=8,
        cost=20000,
        thrust_boost=0.4,
        turn_boost=0.2,
        energy_usage=8,
        compatible_sizes=[SIZE_SMALL, SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL],
        min_tech_level=2,
        description="An improved engine with better performance."
    ),

    "high_performance_engine": Engine(
        name="High Performance Engine",
        space_required=12,
        cost=40000,
        thrust_boost=0.6,
        turn_boost=0.3,
        energy_usage=12,
        compatible_sizes=[SIZE_SMALL, SIZE_MEDIUM, SIZE_LARGE],
        min_tech_level=3,
        description="A top-tier engine providing excellent acceleration and turning."
    ),

    "capital_engine": Engine(
        name="Capital Engine",
        space_required=20,
        cost=80000,
        thrust_boost=0.3,
        turn_boost=0.1,
        energy_usage=20,
        compatible_sizes=[SIZE_LARGE, SIZE_CAPITAL],
        min_tech_level=4,
        description="A massive engine designed for capital ships."
    ),

    # Shields
    "basic_shield": Shield(
        name="Basic Shield",
        space_required=3,
        cost=8000,
        shield_boost=50,
        recharge_rate=1.0,
        energy_usage=3,
        compatible_sizes=[SIZE_SMALL, SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL],
        description="A basic shield generator providing modest protection."
    ),

    "improved_shield": Shield(
        name="Improved Shield",
        space_required=6,
        cost=18000,
        shield_boost=100,
        recharge_rate=1.5,
        energy_usage=5,
        compatible_sizes=[SIZE_SMALL, SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL],
        description="An improved shield with better protection and faster recharge."
    ),

    "heavy_shield": Shield(
        name="Heavy Shield",
        space_required=10,
        cost=35000,
        shield_boost=200,
        recharge_rate=2.0,
        energy_usage=8,
        compatible_sizes=[SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL],
        description="A powerful shield system for larger vessels."
    ),

    "capital_shield": Shield(
        name="Capital Shield",
        space_required=15,
        cost=70000,
        shield_boost=400,
        recharge_rate=3.0,
        energy_usage=15,
        compatible_sizes=[SIZE_LARGE, SIZE_CAPITAL],
        description="A massive shield system designed for capital ships."
    ),

    # Armor
    "light_armor": Armor(
        name="Light Armor",
        space_required=2,
        cost=5000,
        armor_boost=30,
        mass_penalty=0.05,
        compatible_sizes=[SIZE_SMALL, SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL],
        description="Basic armor plating with minimal mass penalty."
    ),

    "medium_armor": Armor(
        name="Medium Armor",
        space_required=5,
        cost=15000,
        armor_boost=80,
        mass_penalty=0.1,
        compatible_sizes=[SIZE_SMALL, SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL],
        description="Standard armor providing good protection with moderate mass penalty."
    ),

    "heavy_armor": Armor(
        name="Heavy Armor",
        space_required=10,
        cost=30000,
        armor_boost=150,
        mass_penalty=0.2,
        compatible_sizes=[SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL],
        description="Heavy armor plating for serious protection at the cost of agility."
    ),

    "capital_armor": Armor(
        name="Capital Armor",
        space_required=20,
        cost=60000,
        armor_boost=300,
        mass_penalty=0.3,
        compatible_sizes=[SIZE_LARGE, SIZE_CAPITAL],
        description="Extremely heavy armor designed for capital ships."
    ),

    # Utility - Cargo Expansions
    "small_cargo_pod": Utility(
        name="Small Cargo Pod",
        space_required=3,
        cost=5000,
        effect={"cargo_space": 10},
        compatible_sizes=[SIZE_SMALL, SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL],
        min_tech_level=1,
        description="Small cargo expansion that adds 10 tons of cargo space."
    ),

    "cargo_expansion": Utility(
        name="Cargo Expansion",
        space_required=5,
        cost=10000,
        effect={"cargo_space": 20},
        compatible_sizes=[SIZE_SMALL, SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL],
        min_tech_level=2,
        description="Standard cargo expansion that adds 20 tons of cargo space."
    ),

    "large_cargo_bay": Utility(
        name="Large Cargo Bay",
        space_required=8,
        cost=18000,
        effect={"cargo_space": 40},
        compatible_sizes=[SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL],
        min_tech_level=2,
        description="Large cargo expansion that adds 40 tons of cargo space."
    ),

    "massive_cargo_hold": Utility(
        name="Massive Cargo Hold",
        space_required=15,
        cost=35000,
        effect={"cargo_space": 100},
        compatible_sizes=[SIZE_LARGE, SIZE_CAPITAL],
        min_tech_level=3,
        description="Enormous cargo expansion that adds 100 tons of cargo space."
    ),

    # Utility - Other
    "fuel_tank": Utility(
        name="Extended Fuel Tank",
        space_required=3,
        cost=5000,
        effect={"fuel_capacity": 50},
        compatible_sizes=[SIZE_SMALL, SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL],
        min_tech_level=1,
        description="Increases fuel capacity for longer jumps."
    ),

    "scanner": Utility(
        name="Advanced Scanner",
        space_required=2,
        cost=8000,
        effect={"scan_range": 2.0},
        energy_usage=1,
        compatible_sizes=[SIZE_SMALL, SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL],
        min_tech_level=2,
        description="Improves scanning range and detail."
    ),

    "auto_repair": Utility(
        name="Auto-Repair System",
        space_required=4,
        cost=15000,
        effect={"repair_rate": 1.0},
        energy_usage=2,
        compatible_sizes=[SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL],
        min_tech_level=3,
        description="Automatically repairs hull damage over time."
    ),

    # Special
    "cloaking_device": Special(
        name="Cloaking Device",
        space_required=8,
        cost=50000,
        effects={"cloak": True, "energy_drain": 10},
        energy_usage=20,
        compatible_sizes=[SIZE_SMALL, SIZE_MEDIUM, SIZE_LARGE],
        min_tech_level=4,
        description="Makes the ship invisible to sensors when activated."
    ),

    "jump_drive": Special(
        name="Jump Drive",
        space_required=10,
        cost=100000,
        effects={"jump_range": 2.0},
        energy_usage=50,
        compatible_sizes=[SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL],
        min_tech_level=4,
        description="Allows jumping to systems beyond normal range."
    ),

    "fighter_bay": Special(
        name="Fighter Bay",
        space_required=15,
        cost=120000,
        effects={"fighter_capacity": 3},
        energy_usage=5,
        compatible_sizes=[SIZE_LARGE, SIZE_CAPITAL],
        min_tech_level=4,
        description="Allows carrying and deploying fighter craft."
    ),

    "point_defense": Special(
        name="Point Defense System",
        space_required=5,
        cost=25000,
        effects={"missile_defense": 0.5},
        energy_usage=5,
        compatible_sizes=[SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL],
        min_tech_level=3,
        description="Automatically shoots down incoming missiles."
    ),

    "targeting_computer": Special(
        name="Targeting Computer",
        space_required=2,
        cost=15000,
        effects={"targeting_accuracy": 0.25, "missile_lock_time": -0.5},
        energy_usage=2,
        compatible_sizes=[SIZE_SMALL, SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL],
        min_tech_level=2,
        description="Improves weapon accuracy and reduces missile lock-on time."
    ),
}

def get_outfit_by_name(name):
    """Get an outfit by its name (case insensitive)."""
    for outfit_id, outfit in OUTFITS.items():
        if outfit.name.lower() == name.lower():
            return outfit
    return None

def get_outfit_by_id(outfit_id):
    """Get an outfit by its ID."""
    return OUTFITS.get(outfit_id)

def get_outfits_by_category(category):
    """Get all outfits of a specific category."""
    return [outfit for outfit in OUTFITS.values() if outfit.category == category]

def get_compatible_outfits(ship_size):
    """Get all outfits compatible with a specific ship size."""
    return [outfit for outfit in OUTFITS.values() if outfit.is_compatible_with_size(ship_size)]

def get_outfits_by_tech_level(tech_level, include_lower=True):
    """
    Get all outfits available at a specific tech level.

    Args:
        tech_level (int): The tech level to check
        include_lower (bool): If True, include outfits from lower tech levels

    Returns:
        list: List of outfits available at the specified tech level
    """
    if include_lower:
        return [outfit for outfit in OUTFITS.values() if outfit.min_tech_level <= tech_level]
    else:
        return [outfit for outfit in OUTFITS.values() if outfit.min_tech_level == tech_level]

def get_available_outfits_for_purchase(tech_level, ship_size):
    """
    Get all outfits available for purchase at a specific tech level and compatible with a ship size.

    Args:
        tech_level (int): The tech level of the outfitter
        ship_size (str): The size of the ship

    Returns:
        list: List of outfits available for purchase
    """
    return [outfit for outfit in OUTFITS.values()
            if outfit.min_tech_level <= tech_level and outfit.is_compatible_with_size(ship_size)]
